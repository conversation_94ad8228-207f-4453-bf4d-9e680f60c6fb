// The MIT License (MIT)
//
// Copyright (c) 2015-2016 the fiat-crypto authors (see the AUTHORS file).
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.

// This file is generated from
//    ./make_curve25519_tables.py > curve25519_tables.h

static const fe d = {{
    56195235, 13857412, 51736253, 6949390, 114729, 24766616,
    60832955, 30306712, 48412415, 21499315
}};

static const fe sqrtm1 = {{
    34513072, 25610706, 9377949, 3500415, 12389472,
    33281959, 41962654, 31548777, 326685, 11406482
}};

static const fe d2 = {{
    45281625, 27714825, 36363642, 13898781, 229458, 15978800,
    54557047, 27058993, 29715967, 9444199
}};

// Bi[i] = (2*i+1)*B
static const ge_precomp Bi[8] = {
    {
        {{25967493, 19198397, 29566455, 3660896, 54414519, 4014786, 27544626,
          21800161, 61029707, 2047604}},
        {{54563134, 934261, 64385954, 3049989, 66381436, 9406985, 12720692,
          5043384, 19500929, 18085054}},
        {{58370664, 4489569, 9688441, 18769238, 10184608, 21191052, 29287918,
          11864899, 42594502, 29115885}},
    },
    {
        {{15636272, 23865875, 24204772, 25642034, 616976, 16869170, 27787599,
          18782243, 28944399, 32004408}},
        {{16568933, 4717097, 55552716, 32452109, 15682895, 21747389, 16354576,
          21778470, 7689661, 11199574}},
        {{30464137, 27578307, 55329429, 17883566, 23220364, 15915852, 7512774,
          10017326, 49359771, 23634074}},
    },
    {
        {{10861363, 11473154, 27284546, 1981175, 37044515, 12577860, 32867885,
          14515107, 51670560, 10819379}},
        {{4708026, 6336745, 20377586, 9066809, 55836755, 6594695, 41455196,
          12483687, 54440373, 5581305}},
        {{19563141, 16186464, 37722007, 4097518, 10237984, 29206317, 28542349,
          13850243, 43430843, 17738489}},
    },
    {
        {{5153727, 9909285, 1723747, 30776558, 30523604, 5516873, 19480852,
          5230134, 43156425, 18378665}},
        {{36839857, 30090922, 7665485, 10083793, 28475525, 1649722, 20654025,
          16520125, 30598449, 7715701}},
        {{28881826, 14381568, 9657904, 3680757, 46927229, 7843315, 35708204,
          1370707, 29794553, 32145132}},
    },
    {
        {{44589871, 26862249, 14201701, 24808930, 43598457, 8844725, 18474211,
          32192982, 54046167, 13821876}},
        {{60653668, 25714560, 3374701, 28813570, 40010246, 22982724, 31655027,
          26342105, 18853321, 19333481}},
        {{4566811, 20590564, 38133974, 21313742, 59506191, 30723862, 58594505,
          23123294, 2207752, 30344648}},
    },
    {
        {{41954014, 29368610, 29681143, 7868801, 60254203, 24130566, 54671499,
          32891431, 35997400, 17421995}},
        {{25576264, 30851218, 7349803, 21739588, 16472781, 9300885, 3844789,
          15725684, 171356, 6466918}},
        {{23103977, 13316479, 9739013, 17404951, 817874, 18515490, 8965338,
          19466374, 36393951, 16193876}},
    },
    {
        {{33587053, 3180712, 64714734, 14003686, 50205390, 17283591, 17238397,
          4729455, 49034351, 9256799}},
        {{41926547, 29380300, 32336397, 5036987, 45872047, 11360616, 22616405,
          9761698, 47281666, 630304}},
        {{53388152, 2639452, 42871404, 26147950, 9494426, 27780403, 60554312,
          17593437, 64659607, 19263131}},
    },
    {
        {{63957664, 28508356, 9282713, 6866145, 35201802, 32691408, 48168288,
          15033783, 25105118, 25659556}},
        {{42782475, 15950225, 35307649, 18961608, 55446126, 28463506, 1573891,
          30928545, 2198789, 17749813}},
        {{64009494, 10324966, 64867251, 7453182, 61661885, 30818928, 53296841,
          17317989, 34647629, 21263748}},
    },
};
