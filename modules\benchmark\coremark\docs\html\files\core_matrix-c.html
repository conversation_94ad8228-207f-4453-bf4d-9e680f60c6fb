<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>/cygdrive/d/dev/code/coremark/core_matrix.c - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CFile"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="core_matrix.c"></a>core_matrix.c</h1><div class=CBody><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#core_matrix.c" >core_matrix.c</a></td><td class=SDescription></td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Description" >Description</a></td><td class=SDescription>Matrix manipulation benchmark</td></tr><tr class="SGroup"><td class=SEntry><a href="#Functions" >Functions</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#core_bench_matrix" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">core_bench_matrix</a></td><td class=SDescription>Benchmark function</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#matrix_test" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')">matrix_test</a></td><td class=SDescription>Perform matrix manipulation.</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#matrix_sum" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')">matrix_sum</a></td><td class=SDescription>Calculate a function that depends on the values of elements in the matrix.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#matrix_mul_const" id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')">matrix_mul_const</a></td><td class=SDescription>Multiply a matrix by a constant. </td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#matrix_add_const" id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')">matrix_add_const</a></td><td class=SDescription>Add a constant value to all elements of a matrix.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#matrix_mul_vect" id=link6 onMouseOver="ShowTip(event, 'tt6', 'link6')" onMouseOut="HideTip('tt6')">matrix_mul_vect</a></td><td class=SDescription>Multiply a matrix by a vector. </td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#matrix_mul_matrix" id=link7 onMouseOver="ShowTip(event, 'tt7', 'link7')" onMouseOut="HideTip('tt7')">matrix_mul_matrix</a></td><td class=SDescription>Multiply a matrix by a matrix. </td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#matrix_mul_matrix_bitextract" id=link8 onMouseOver="ShowTip(event, 'tt8', 'link8')" onMouseOut="HideTip('tt8')">matrix_mul_matrix_bitextract</a></td><td class=SDescription>Multiply a matrix by a matrix, and extract some bits from the result. </td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Description"></a>Description</h3><div class=CBody><p>Matrix manipulation benchmark</p><p>This very simple algorithm forms the basis of many more complex algorithms.</p><p>The tight inner loop is the focus of many optimizations (compiler as well as hardware based) and is thus relevant for embedded processing.</p><h4 class=CHeading>The total available data space will be divided to 3 parts</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>NxN Matrix A</td><td class=CDLDescription>initialized with small values (upper 3/4 of the bits all zero).</td></tr><tr><td class=CDLEntry>NxN Matrix B</td><td class=CDLDescription>initialized with medium values (upper half of the bits all zero).</td></tr><tr><td class=CDLEntry>NxN Matrix C</td><td class=CDLDescription>used for the result.</td></tr></table><p>The actual values for A and B must be derived based on input that is not available at compile time.</p></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="Functions"></a>Functions</h3></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_bench_matrix"></a>core_bench_matrix</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_u16 core_bench_matrix(</td><td class=PType nowrap>mat_params&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed,</td></tr><tr><td></td><td class=PType nowrap>ee_u16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>crc</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Benchmark function</p><p>Iterate <a href="#matrix_test" class=LFunction id=link9 onMouseOver="ShowTip(event, 'tt2', 'link9')" onMouseOut="HideTip('tt2')">matrix_test</a> N times, changing the matrix values slightly by a constant amount each time.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="matrix_test"></a>matrix_test</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s16 matrix_test(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Perform matrix manipulation.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>N</td><td class=CDLDescription>Dimensions of the matrix.</td></tr><tr><td class=CDLEntry>C</td><td class=CDLDescription>memory for result matrix.</td></tr><tr><td class=CDLEntry>A</td><td class=CDLDescription>input matrix</td></tr><tr><td class=CDLEntry>B</td><td class=CDLDescription>operator matrix (not changed during operations)</td></tr></table><h4 class=CHeading>Returns</h4><p>A CRC value that captures all results calculated in the function.&nbsp; In particular, crc of the value calculated on the result matrix after each step by <a href="#matrix_sum" class=LFunction id=link10 onMouseOver="ShowTip(event, 'tt3', 'link10')" onMouseOut="HideTip('tt3')">matrix_sum</a>.</p><h4 class=CHeading>Operation</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>1</td><td class=CDLDescription>Add a constant value to all elements of a matrix.</td></tr><tr><td class=CDLEntry>2</td><td class=CDLDescription>Multiply a matrix by a constant.</td></tr><tr><td class=CDLEntry>3</td><td class=CDLDescription>Multiply a matrix by a vector.</td></tr><tr><td class=CDLEntry>4</td><td class=CDLDescription>Multiply a matrix by a matrix.</td></tr><tr><td class=CDLEntry>5</td><td class=CDLDescription>Add a constant value to all elements of a matrix.</td></tr></table><p>After the last step, matrix A is back to original contents.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="matrix_sum"></a>matrix_sum</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s16 matrix_sum(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>clipval</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Calculate a function that depends on the values of elements in the matrix.</p><p>For each element, accumulate into a temporary variable.</p><p>As long as this value is under the parameter clipval, add 1 to the result if the element is bigger then the previous.</p><p>Otherwise, reset the accumulator and add 10 to the result.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="matrix_mul_const"></a>matrix_mul_const</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_const(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Multiply a matrix by a constant.&nbsp; This could be used as a scaler for instance.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="matrix_add_const"></a>matrix_add_const</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_add_const(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Add a constant value to all elements of a matrix.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="matrix_mul_vect"></a>matrix_mul_vect</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_vect(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Multiply a matrix by a vector.&nbsp; This is common in many simple filters (e.g. fir where a vector of coefficients is applied to the matrix.)</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="matrix_mul_matrix"></a>matrix_mul_matrix</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_matrix(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Multiply a matrix by a matrix.&nbsp; Basic code is used in many algorithms, mostly with minor changes such as scaling.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="matrix_mul_matrix_bitextract"></a>matrix_mul_matrix_bitextract</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_matrix_bitextract(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Multiply a matrix by a matrix, and extract some bits from the result.&nbsp; Basic code is used in many algorithms, mostly with minor changes such as scaling.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile id=MSelected>core_matrix.c</div></div><div class=MEntry><div class=MFile><a href="PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="../index/BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_u16 core_bench_matrix(</td><td class=PType nowrap>mat_params&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed,</td></tr><tr><td></td><td class=PType nowrap>ee_u16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>crc</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Benchmark function</div></div><div class=CToolTip id="tt2"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s16 matrix_test(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Perform matrix manipulation.</div></div><div class=CToolTip id="tt3"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s16 matrix_sum(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>clipval</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Calculate a function that depends on the values of elements in the matrix.</div></div><div class=CToolTip id="tt4"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_const(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a constant. </div></div><div class=CToolTip id="tt5"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_add_const(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Add a constant value to all elements of a matrix.</div></div><div class=CToolTip id="tt6"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_vect(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a vector. </div></div><div class=CToolTip id="tt7"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_matrix(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a matrix. </div></div><div class=CToolTip id="tt8"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_matrix_bitextract(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a matrix, and extract some bits from the result. </div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>