CONFIG_SIZE_OPTIMIZATIONS=y

CONFIG_SYSTEM_CLOCK_NO_WAIT=y
CONFIG_PM=n

CONFIG_MAIN_STACK_SIZE=10240
CONFIG_MBEDTLS_CFG_FILE="mcuboot-mbedtls-cfg.h"

CONFIG_BOOT_MAX_IMG_SECTORS=2048
CONFIG_BOOT_SIGNATURE_TYPE_RSA=y

# Flash
CONFIG_FLASH=y
CONFIG_BOOT_ERASE_PROGRESSIVELY=y
CONFIG_SOC_FLASH_NRF_EMULATE_ONE_BYTE_WRITE_ACCESS=y
CONFIG_FPROTECT=y

# Serial
CONFIG_SERIAL=y
CONFIG_UART_LINE_CTRL=y

# MCUBoot serial
CONFIG_GPIO=y
CONFIG_GPIO_NRFX_INTERRUPT=n
CONFIG_MCUBOOT_SERIAL=y
CONFIG_MCUBOOT_SERIAL_DIRECT_IMAGE_UPLOAD=y
CONFIG_BOOT_SERIAL_CDC_ACM=y

# Required by QSPI
CONFIG_NORDIC_QSPI_NOR=y
CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE=4096
CONFIG_NORDIC_QSPI_NOR_STACK_WRITE_BUFFER_SIZE=16

# Required by USB and QSPI
CONFIG_MULTITHREADING=y

# USB
CONFIG_BOARD_SERIAL_BACKEND_CDC_ACM=n
CONFIG_USB_DEVICE_REMOTE_WAKEUP=n
CONFIG_USB_DEVICE_MANUFACTURER="Nordic Semiconductor ASA"
CONFIG_USB_DEVICE_PRODUCT="Bootloader Thingy:53"
CONFIG_USB_DEVICE_VID=0x1915
CONFIG_USB_DEVICE_PID=0x5300
CONFIG_USB_CDC_ACM=y

# Decrease memory footprint
CONFIG_CBPRINTF_NANO=y
CONFIG_TIMESLICING=n
CONFIG_BOOT_BANNER=n
CONFIG_CONSOLE=n
CONFIG_CONSOLE_HANDLER=n
CONFIG_UART_CONSOLE=n
CONFIG_USE_SEGGER_RTT=n
CONFIG_LOG=n
CONFIG_ERRNO=n
CONFIG_PRINTK=n
CONFIG_RESET_ON_FATAL_ERROR=n
CONFIG_SPI=n
CONFIG_I2C=n
CONFIG_UART_NRFX=n

# The following configurations are required to support simultaneous multi image update
CONFIG_PCD_APP=y
CONFIG_UPDATEABLE_IMAGE_NUMBER=2
CONFIG_BOOT_UPGRADE_ONLY=y
# The network core cannot access external flash directly. The flash simulator must be used to
# provide a memory region that is used to forward the new firmware to the network core.
CONFIG_FLASH_SIMULATOR=y
CONFIG_FLASH_SIMULATOR_DOUBLE_WRITES=y
CONFIG_FLASH_SIMULATOR_STATS=n

# Enable custom command to erase settings partition.
CONFIG_ENABLE_MGMT_PERUSER=y
CONFIG_BOOT_MGMT_CUSTOM_STORAGE_ERASE=y
