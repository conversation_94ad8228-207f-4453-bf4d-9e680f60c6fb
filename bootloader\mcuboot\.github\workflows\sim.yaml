# For development, trigger this on any push.
on:
  push:
    branches:
      - main
  pull_request:

name: Sim

concurrency:
  group: sim-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  environment:
    strategy:
      matrix:
        features:
        - "sig-ecdsa,sig-ecdsa-mbedtls,sig-ed25519,enc-kw,bootstrap"
        - "sig-rsa,sig-rsa3072,overwrite-only,validate-primary-slot,swap-move"
        - "enc-rsa,enc-rsa max-align-32"
        - "enc-aes256-rsa,enc-aes256-rsa max-align-32"
        - "enc-ec256,enc-ec256 max-align-32"
        - "enc-aes256-ec256,enc-aes256-ec256 max-align-32"
        - "enc-x25519,enc-x25519 max-align-32"
        - "enc-aes256-x25519,enc-aes256-x25519 max-align-32"
        - "sig-rsa overwrite-only,sig-ecdsa overwrite-only,sig-ecdsa-mbedtls overwrite-only,multiimage overwrite-only"
        - "sig-rsa validate-primary-slot,sig-ecdsa validate-primary-slot,sig-ecdsa-mbedtls validate-primary-slot,sig-rsa multiimage validate-primary-slot"
        - "enc-kw overwrite-only,enc-kw overwrite-only max-align-32"
        - "enc-rsa overwrite-only,enc-rsa overwrite-only max-align-32"
        - "enc-aes256-kw overwrite-only,enc-aes256-kw overwrite-only max-align-32"
        - "sig-rsa enc-rsa validate-primary-slot,swap-move enc-rsa sig-rsa validate-primary-slot bootstrap"
        - "sig-rsa enc-kw validate-primary-slot bootstrap,sig-ed25519 enc-x25519 validate-primary-slot"
        - "sig-ecdsa enc-kw validate-primary-slot"
        - "sig-ecdsa-mbedtls enc-kw validate-primary-slot"
        - "sig-rsa validate-primary-slot overwrite-only,sig-rsa validate-primary-slot overwrite-only max-align-32"
        - "sig-ecdsa enc-ec256 validate-primary-slot"
        - "sig-ecdsa-mbedtls enc-ec256-mbedtls validate-primary-slot"
        - "sig-ecdsa-mbedtls enc-aes256-ec256 validate-primary-slot"
        - "sig-rsa validate-primary-slot overwrite-only downgrade-prevention"
        - "sig-rsa validate-primary-slot ram-load"
        - "sig-rsa enc-rsa validate-primary-slot ram-load"
        - "sig-rsa validate-primary-slot direct-xip"
        - "sig-rsa validate-primary-slot ram-load multiimage"
        - "sig-rsa validate-primary-slot direct-xip multiimage"
        - "sig-ecdsa hw-rollback-protection multiimage"
        - "sig-ecdsa-psa,sig-ecdsa-psa sig-p384"
        - "ram-load enc-aes256-kw multiimage"
        - "ram-load enc-aes256-kw sig-ecdsa-mbedtls multiimage"
    runs-on: ubuntu-latest
    env:
      MULTI_FEATURES: ${{ matrix.features }}
    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0
        submodules: recursive
    - name: Print the environment
      run: |
        uname -a
        lscpu
        free
        pwd
    - name: Signed commit check
      if: ${{ github.event_name == 'pull_request' }}
      run: |
        ./ci/check-signed-off-by.sh
    - name: Install stable Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
    - name: Sim install
      run: |
        ./ci/sim_install.sh
    - name: Sim run
      run: |
        ./ci/sim_run.sh
