<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>Variable Index - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="IndexPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=IPageTitle>Variable Index</div><div class=INavigationBar>$#! &middot; 0-9 &middot; A &middot; B &middot; <a href="#C">C</a> &middot; <a href="#D">D</a> &middot; E &middot; F &middot; G &middot; H &middot; I &middot; J &middot; K &middot; <a href="#L">L</a> &middot; M &middot; N &middot; <a href="#O">O</a> &middot; <a href="#P">P</a> &middot; Q &middot; <a href="#R">R</a> &middot; <a href="#S">S</a> &middot; T &middot; U &middot; V &middot; W &middot; X &middot; Y &middot; Z</div><table border=0 cellspacing=0 cellpadding=0><tr><td class=IHeading id=IFirstHeading><a name="C"></a>C</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-mak.html#CC" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')" class=ISymbol>CC</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>CFLAGS</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#CFLAGS" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#CFLAGS" id=link3 onMouseOver="ShowTip(event, 'tt2', 'link3')" onMouseOut="HideTip('tt2')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=IHeading><a name="D"></a>D</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#default_num_contexts" id=link4 onMouseOver="ShowTip(event, 'tt3', 'link4')" onMouseOut="HideTip('tt3')" class=ISymbol>default_num_contexts</a></td></tr><tr><td class=IHeading><a name="L"></a>L</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>LFLAGS_END</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#LFLAGS_END" id=link5 onMouseOver="ShowTip(event, 'tt4', 'link5')" onMouseOut="HideTip('tt4')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#LFLAGS_END" id=link6 onMouseOver="ShowTip(event, 'tt4', 'link6')" onMouseOut="HideTip('tt4')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-mak.html#LOAD" id=link7 onMouseOver="ShowTip(event, 'tt5', 'link7')" onMouseOut="HideTip('tt5')" class=ISymbol>LOAD</a></td></tr><tr><td class=IHeading><a name="O"></a>O</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>OPATH</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#OPATH"  class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#OPATH"  class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>OUTFLAG</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#OUTFLAG" id=link8 onMouseOver="ShowTip(event, 'tt6', 'link8')" onMouseOut="HideTip('tt6')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#OUTFLAG" id=link9 onMouseOver="ShowTip(event, 'tt6', 'link9')" onMouseOut="HideTip('tt6')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=IHeading><a name="P"></a>P</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>PERL</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#PERL" id=link10 onMouseOver="ShowTip(event, 'tt7', 'link10')" onMouseOut="HideTip('tt7')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#PERL" id=link11 onMouseOver="ShowTip(event, 'tt7', 'link11')" onMouseOut="HideTip('tt7')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>PORT_OBJS</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#PORT_OBJS" id=link12 onMouseOver="ShowTip(event, 'tt8', 'link12')" onMouseOut="HideTip('tt8')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#PORT_OBJS" id=link13 onMouseOver="ShowTip(event, 'tt8', 'link13')" onMouseOut="HideTip('tt8')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-mak.html#PORT_SRCS" id=link14 onMouseOver="ShowTip(event, 'tt9', 'link14')" onMouseOut="HideTip('tt9')" class=ISymbol>PORT_SRCS</a></td></tr><tr><td class=IHeading><a name="R"></a>R</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-mak.html#RUN" id=link15 onMouseOver="ShowTip(event, 'tt10', 'link15')" onMouseOut="HideTip('tt10')" class=ISymbol>RUN</a></td></tr><tr><td class=IHeading><a name="S"></a>S</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>SEPARATE_COMPILE</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#SEPARATE_COMPILE" id=link16 onMouseOver="ShowTip(event, 'tt11', 'link16')" onMouseOut="HideTip('tt11')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#SEPARATE_COMPILE" id=link17 onMouseOver="ShowTip(event, 'tt11', 'link17')" onMouseOut="HideTip('tt11')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr></table>
<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CVariable>Use this flag to define compiler to use</div></div><div class=CToolTip id="tt2"><div class=CVariable>Use this flag to define compiler options. </div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt3"><div class=CVariable><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td>extern ee_u32 default_num_contexts</td></tr></table></blockquote>Number of contexts to spawn in multicore context. </div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt4"><div class=CVariable>Define any libraries needed for linking or other flags that should come at the end of the link line (e.g. </div></div><div class=CToolTip id="tt5"><div class=CVariable>Define this flag if you need to load to a target, as in a cross compile environment.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt6"><div class=CVariable>Use this flag to define how to to get an executable (e.g -o)</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt7"><div class=CVariable>Define perl executable to calculate the geomean if running separate.</div></div><div class=CToolTip id="tt8"><div class=CVariable>Port specific object files can be added here</div></div><div class=CToolTip id="tt9"><div class=CVariable>Port specific source files can be added here</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt10"><div class=CVariable>Define this flag if running does not consist of simple invocation of the binary. </div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt11"><div class=CVariable>Define if you need to separate compilation from link stage. </div></div><!--END_ND_TOOLTIPS-->

</div><!--Index-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="../files/readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="../files/release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../files/core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex id=MSelected>Variables</div></div><div class=MEntry><div class=MIndex><a href="BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->


<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>