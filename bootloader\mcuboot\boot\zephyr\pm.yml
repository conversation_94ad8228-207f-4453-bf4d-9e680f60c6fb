#include <zephyr/autoconf.h>

mcuboot:
  size: CONFIG_PM_PARTITION_SIZE_MCUBOOT
  placement:
    before: [mcuboot_primary]
#if defined(CONFIG_HIDE_CHILD_PARENT_CONFIG)
    align: {end: 0x1000}
#endif

mcuboot_primary_app:
  # All images to be placed in MCUboot's slot 0 should be placed in this
  # partition
  span: [app]

mcuboot_primary:
  span: [mcuboot_pad, mcuboot_primary_app]

# Partition for secondary slot is not created if building in single application
# slot configuration.
#if !defined(CONFIG_SINGLE_APPLICATION_SLOT) && !defined(CONFIG_BOOT_DIRECT_XIP)
mcuboot_secondary:
  share_size: [mcuboot_primary]
#if defined(CONFIG_PM_EXTERNAL_FLASH_MCUBOOT_SECONDARY)
  region: external_flash
  placement:
    align: {start: 4}
#else
  placement:
    align: {start: CONFIG_FPROTECT_BLOCK_SIZE}
    align_next: CONFIG_FPROTECT_BLOCK_SIZE # Ensure that the next partition does not interfere with this image
    after: mcuboot_primary
#endif /* CONFIG_PM_EXTERNAL_FLASH_MCUBOOT_SECONDARY */

#endif /* !defined(CONFIG_SINGLE_APPLICATION_SLOT) && !defined(CONFIG_BOOT_DIRECT_XIP) */

#if CONFIG_BOOT_DIRECT_XIP

# Direct XIP is enabled, reserve area for metadata (padding) and name the
# partition so that its clear that it is not the secondary slot, but the direct
# XIP alternative.

mcuboot_secondary_pad:
  share_size: mcuboot_pad
  placement:
    after: mcuboot_primary
    align: {start: CONFIG_FPROTECT_BLOCK_SIZE}

mcuboot_secondary_app:
  share_size: mcuboot_primary_app
  placement:
    after: mcuboot_secondary_pad

mcuboot_secondary:
  span: [mcuboot_secondary_pad, mcuboot_secondary_app]

#endif /* CONFIG_BOOT_DIRECT_XIP */

#if CONFIG_BOOT_SWAP_USING_SCRATCH
mcuboot_scratch:
  size: CONFIG_PM_PARTITION_SIZE_MCUBOOT_SCRATCH
  placement:
    after: app
    align: {start: CONFIG_FPROTECT_BLOCK_SIZE}
#endif /* CONFIG_BOOT_SWAP_USING_SCRATCH */

# Padding placed before image to boot. This reserves space for the MCUboot image header
# and it ensures that the boot image gets linked with the correct address offset in flash.
mcuboot_pad:
    # MCUboot pad must be placed before the primary application partition.
    # The primary application partition includes the secure firmware if present.
  size: CONFIG_PM_PARTITION_SIZE_MCUBOOT_PAD
  placement:
    before: [mcuboot_primary_app]
#ifdef CONFIG_FPROTECT
    align: {start: CONFIG_FPROTECT_BLOCK_SIZE}
#endif

#if (CONFIG_NRF53_MCUBOOT_PRIMARY_1_RAM_FLASH)
mcuboot_primary_1:
  region: ram_flash
  size: CONFIG_NRF53_RAM_FLASH_SIZE
#endif /* CONFIG_NRF53_MULTI_IMAGE_UPDATE */

#if (CONFIG_NRF53_MULTI_IMAGE_UPDATE)
mcuboot_secondary_1:
  region: external_flash
  size: CONFIG_NRF53_RAM_FLASH_SIZE

#endif /* CONFIG_NRF53_MULTI_IMAGE_UPDATE */
