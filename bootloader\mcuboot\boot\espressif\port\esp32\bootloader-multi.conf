# SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
#
# SPDX-License-Identifier: Apache-2.0

CONFIG_ESP_FLASH_SIZE=4MB
CONFIG_ESP_BOOTLOADER_SIZE=0xF000
CONFIG_ESP_BOOTLOADER_OFFSET=0x1000
# Example of values to be used when multi image is enabled
# Notice that the OS layer and update agent must be aware
# of these regions
CONFIG_ESP_APPLICATION_SIZE=0x80000
CONFIG_ESP_IMAGE0_PRIMARY_START_ADDRESS=0x10000
CONFIG_ESP_IMAGE0_SECONDARY_START_ADDRESS=0x90000
CONFIG_ESP_IMAGE1_PRIMARY_START_ADDRESS=0x110000
CONFIG_ESP_IMAGE1_SECONDARY_START_ADDRESS=0x190000
CONFIG_ESP_SCRATCH_OFFSET=0x210000
CONFIG_ESP_SCRATCH_SIZE=0x40000
CONFIG_ESP_MCUBOOT_WDT_ENABLE=y

CONFIG_ESP_CONSOLE_UART=y
CONFIG_ESP_CONSOLE_UART_NUM=0
# Configures alternative UART port for console printing
# CONFIG_ESP_CONSOLE_UART_CUSTOM=y
# CONFIG_ESP_CONSOLE_UART_TX_GPIO=26
# CONFIG_ESP_CONSOLE_UART_RX_GPIO=25

# Enables multi image, if it is not defined, it is assumed
# only one updatable image
# CONFIG_ESP_IMAGE_NUMBER=2

# Enables multi image boot on independent processors
# (main host OS is not responsible for booting the second image)
# Use only with CONFIG_ESP_IMAGE_NUMBER=2
# CONFIG_ESP_MULTI_PROCESSOR_BOOT=y
