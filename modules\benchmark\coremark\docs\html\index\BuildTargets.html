<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>Build Target Index - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="IndexPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=IPageTitle>Build Target Index</div><div class=INavigationBar>$#! &middot; 0-9 &middot; A &middot; B &middot; C &middot; D &middot; E &middot; F &middot; G &middot; H &middot; I &middot; J &middot; K &middot; L &middot; M &middot; N &middot; O &middot; <a href="#P">P</a> &middot; Q &middot; R &middot; S &middot; T &middot; U &middot; V &middot; W &middot; X &middot; Y &middot; Z</div><table border=0 cellspacing=0 cellpadding=0><tr><td class=IHeading id=IFirstHeading><a name="P"></a>P</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_postbuild</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postbuild" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postbuild" id=link2 onMouseOver="ShowTip(event, 'tt1', 'link2')" onMouseOut="HideTip('tt1')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_postload</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postload" id=link3 onMouseOver="ShowTip(event, 'tt2', 'link3')" onMouseOut="HideTip('tt2')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postload" id=link4 onMouseOver="ShowTip(event, 'tt2', 'link4')" onMouseOut="HideTip('tt2')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_postrun</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postrun" id=link5 onMouseOver="ShowTip(event, 'tt3', 'link5')" onMouseOut="HideTip('tt3')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postrun" id=link6 onMouseOver="ShowTip(event, 'tt3', 'link6')" onMouseOut="HideTip('tt3')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_prebuild</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_prebuild" id=link7 onMouseOver="ShowTip(event, 'tt4', 'link7')" onMouseOut="HideTip('tt4')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_prebuild" id=link8 onMouseOver="ShowTip(event, 'tt4', 'link8')" onMouseOut="HideTip('tt4')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_preload</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_preload" id=link9 onMouseOver="ShowTip(event, 'tt5', 'link9')" onMouseOut="HideTip('tt5')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_preload" id=link10 onMouseOver="ShowTip(event, 'tt5', 'link10')" onMouseOut="HideTip('tt5')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_prerun</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_prerun" id=link11 onMouseOver="ShowTip(event, 'tt6', 'link11')" onMouseOut="HideTip('tt6')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_prerun" id=link12 onMouseOver="ShowTip(event, 'tt6', 'link12')" onMouseOut="HideTip('tt6')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr></table>
<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CBuildTarget>Generate any files that are needed after actual build end. </div></div><div class=CToolTip id="tt2"><div class=CBuildTarget>Do platform specific after load stuff. </div></div><div class=CToolTip id="tt3"><div class=CBuildTarget>Do platform specific after run stuff. </div></div><div class=CToolTip id="tt4"><div class=CBuildTarget>Generate any files that are needed before actual build starts. </div></div><div class=CToolTip id="tt5"><div class=CBuildTarget>Do platform specific before load stuff. </div></div><div class=CToolTip id="tt6"><div class=CBuildTarget>Do platform specific after run stuff. </div></div><!--END_ND_TOOLTIPS-->

</div><!--Index-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="../files/readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="../files/release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../files/core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex id=MSelected>Build Targets</div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->


<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>