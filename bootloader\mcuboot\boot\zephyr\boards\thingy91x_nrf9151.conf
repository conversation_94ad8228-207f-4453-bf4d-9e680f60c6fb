# MCUBoot settings
CONFIG_BOOT_MAX_IMG_SECTORS=512

CONFIG_SPI=y
CONFIG_SPI_NOR=y
CONFIG_SPI_NOR_FLASH_LAYOUT_PAGE_SIZE=4096
CONFIG_SPI_NOR_SFDP_DEVICETREE=y
CONFIG_MULTITHREADING=y

# Disable <PERSON><PERSON>hy<PERSON> console and use UART for MCUboot serial recovery instead
CONFIG_CONSOLE=n
CONFIG_CONSOLE_HANDLER=n
CONFIG_UART_CONSOLE=n
CONFIG_MCUBOOT_SERIAL=y
CONFIG_MCUBOOT_SERIAL_DIRECT_IMAGE_UPLOAD=y
CONFIG_BOOT_SERIAL_IMG_GRP_IMAGE_STATE=y

CONFIG_PM_EXTERNAL_FLASH_MCUBOOT_SECONDARY=y
CONFIG_PM_OVERRIDE_EXTERNAL_DRIVER_CHECK=y

CONFIG_FW_INFO_FIRMWARE_VERSION=2
