{"name": "mcuboot", "config": {"bootloader-build": {"help": "Build the bootloader, in addition to the MCUboot library.", "macro_name": "MCUBOOT_BOOTLOADER_BUILD", "accepted_values": [true, false], "value": true}, "primary-slot-address": {"help": "Start address of the primary (bootable) image slot. Target-dependent, please set on a per-target basis.", "macro_name": "MCUBOOT_PRIMARY_SLOT_START_ADDR", "required": true}, "slot-size": {"help": "Size of the primary (bootable) image slot, in bytes. Target-dependent, please set on a per-target basis.", "macro_name": "MCUBOOT_SLOT_SIZE", "required": true}, "header-size": {"help": "Size of the header info section, in bytes. Target-dependent, please set on a per-target basis.", "macro_name": "MCUBOOT_HEADER_SIZE", "value": "0x1000"}, "scratch-address": {"help": "Start address of the scratch area. If needed, please set on a per-target basis.", "macro_name": "MCUBOOT_SCRATCH_START_ADDR"}, "scratch-size": {"help": "Size of the scratch area, in bytes. If needed, please set on a per-target basis.", "macro_name": "MCUBOOT_SCRATCH_SIZE"}, "validate-primary-slot": {"help": "Always check the signature of the image in the primary slot before booting, even if no upgrade was performed. This is recommended if the boot time penalty is acceptable.", "macro_name": "MCUBOOT_VALIDATE_PRIMARY_SLOT", "accepted_values": [true, null], "value": true}, "signature-algorithm": {"help": "The algorithm used for digital signing.", "macro_name": "MCUBOOT_SIGNATURE_ALGORITHM", "required": true, "accepted_values": ["SIGNATURE_TYPE_RSA", "SIGNATURE_TYPE_EC256", "SIGNATURE_TYPE_ED25519", "SIGNATURE_TYPE_NONE"], "value": "SIGNATURE_TYPE_RSA"}, "rsa-signature-length": {"help": "If RSA is used for signature algorithm, this specifies the length.", "macro_name": "MCUBOOT_RSA_SIGNATURE_LENGTH", "required": true, "accepted_values": [2048, 3072], "value": 2048}, "crypto-backend": {"help": "The crypto library backend. NOTE: TinyCrypt is currently only supported with GCC for Mbed-OS builds.", "macro_name": "MCUBOOT_CRYPTO_BACKEND", "required": true, "accepted_values": ["MBEDTLS", "TINYCRYPT"], "value": "MBEDTLS"}, "overwrite-only": {"help": "The default is to support A/B image swapping with rollback. A simpler code path, which only supports overwriting the existing image with the update image, is also available. (null to disable)", "macro_name": "MCUBOOT_OVERWRITE_ONLY", "accepted_values": [true, null], "value": null}, "overwrite-only-fast": {"help": "Only erase and overwrite those primary slot sectors needed to install the new image, rather than the entire image slot.", "macro_name": "MCUBOOT_OVERWRITE_ONLY_FAST", "accepted_values": [true, null], "value": null}, "log-enable": {"help": "Enable MCUboot logging. Must also enable mbed-trace", "macro_name": "MCUBOOT_HAVE_LOGGING", "accepted_values": [true, null], "value": null}, "log-level": {"help": "Verbosity of MCUboot logging.", "macro_name": "MCUBOOT_LOG_LEVEL", "accepted_values": ["MCUBOOT_LOG_LEVEL_OFF", "MCUBOOT_LOG_LEVEL_ERROR", "MCUBOOT_LOG_LEVEL_WARN", "MCUBOOT_LOG_LEVEL_INFO", "MCUBOOT_LOG_LEVEL_DEBUG"], "value": "MCUBOOT_LOG_LEVEL_OFF"}, "log-bootloader-only": {"help": "Exclude non-bootloader logs from Mbed OS (e.g. underlying storage).", "macro_name": "MCUBOOT_LOG_BOOTLOADER_ONLY", "accepted_values": [true, false], "value": true}, "max-img-sectors": {"help": "Maximum number of flash sectors per image slot. Target-dependent, please set on a per-target basis.", "macro_name": "MCUBOOT_MAX_IMG_SECTORS", "required": true}, "read-granularity": {"help": "Granularity of read operations, in bytes. Enables a workaround if your block device does not support reading a single byte at a time. If this is used, it should be at least the value of your specific <blockdevice>->get_read_size() result.", "macro_name": "MCUBOOT_READ_GRANULARITY", "value": null}, "hardware-key": {"help": "Use hardware key (NOT TESTED)", "macro_name": "MCUBOOT_HW_KEY", "accepted_values": [true, null], "value": null}, "boot-swap-move": {"help": "Boot swap using move (NOT TESTED)", "macro_name": "MCUBOOT_SWAP_USING_MOVE", "accepted_values": [true, null], "value": null}, "updateable-image-number": {"help": "Updateable image number (NOT TESTED)", "macro_name": "MCUBOOT_IMAGE_NUMBER"}, "MCUBOOT_SWAP_SAVE_ENCTLV": {"help": "Swap save enctlv (NOT TESTED)", "macro_name": "MCUBOOT_IMAGE_NUMBER", "value": null}, "encrypt-rsa": {"help": "Encrypt images using RSA (NOT TESTED)", "macro_name": "MCUBOOT_ENCRYPT_RSA", "accepted_values": [true, null], "value": null}, "encrypt-ec256": {"help": "Encrypt images using EC256 (NOT TESTED)", "macro_name": "MCUBOOT_ENCRYPT_EC256", "accepted_values": [true, null], "value": null}, "encrypt-x25519": {"help": "Encrypt images using X25519 (NOT TESTED)", "macro_name": "MCUBOOT_ENCRYPT_X25519", "accepted_values": [true, null], "value": null}, "bootstrap": {"help": "Bootstrap (NOT TESTED)", "macro_name": "MCUBOOT_BOOTSTRAP", "value": null}, "use-bench": {"help": "Use bench (NOT TESTED)", "macro_name": "MCUBOOT_USE_BENCH", "value": null}, "downgrade-prevention": {"help": "Prevent downgrades (NOT TESTED)", "macro_name": "MCUBOOT_DOWNGRADE_PREVENTION", "value": null}, "hw-rollback-protection": {"help": "Hardware rollback protection (NOT TESTED)", "macro_name": "MCUBOOT_HW_ROLLBACK_PROT", "value": null}, "measured-boot": {"help": "Measured boot (NOT TESTED)", "macro_name": "MCUBOOT_MEASURED_BOOT", "value": null}, "share-data": {"help": "Share data (NOT TESTED)", "macro_name": "MCUBOOT_DATA_SHARING", "value": null}, "share-data-base-address": {"help": "Start of reserved RAM region for data shared between bootloader and application", "macro_name": "MCUBOOT_SHARED_DATA_BASE", "value": null}, "share-data-size": {"help": "Size of reserved RAM region for data shared between bootloader and application", "macro_name": "MCUBOOT_SHARED_DATA_SIZE", "value": null}, "direct-xip": {"help": "Enable ability to boot update candidates in-place.", "macro_name": "MCUBOOT_DIRECT_XIP", "value": null}, "direct-xip-revert": {"help": "Enable XIP revert mechanism. Only valid if direct-xip is also enabled.", "macro_name": "MCUBOOT_DIRECT_XIP_REVERT", "value": null}, "xip-secondary-slot-address": {"help": "Specify start address for secondary slot address in XIP-accessible memory. This is required if direct-xip is enabled.", "value": null}}}