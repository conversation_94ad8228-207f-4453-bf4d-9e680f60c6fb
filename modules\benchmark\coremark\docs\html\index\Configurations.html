<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>Configuration Index</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="IndexPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Generated by Natural Docs, version Development Release 01-12-2008 (1.35 base) -->
<!--  http://www.naturaldocs.org  -->

<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=IPageTitle>Configuration Index</div><div class=INavigationBar>$#! &middot; 0-9 &middot; A &middot; B &middot; C &middot; D &middot; E &middot; F &middot; G &middot; <a href="#H">H</a> &middot; I &middot; J &middot; K &middot; L &middot; <a href="#M">M</a> &middot; N &middot; O &middot; P &middot; Q &middot; R &middot; <a href="#S">S</a> &middot; <a href="#T">T</a> &middot; U &middot; V &middot; W &middot; X &middot; Y &middot; Z</div><table border=0 cellspacing=0 cellpadding=0><tr><td class=IHeading id=IFirstHeading><a name="H"></a>H</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#HAS_FLOAT" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')" class=ISymbol>HAS_FLOAT</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#HAS_STDIO" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')" class=ISymbol>HAS_STDIO</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#HAS_TIME_H" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')" class=ISymbol>HAS_TIME_H</a></td></tr><tr><td class=IHeading><a name="M"></a>M</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#MEM_METHOD" id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')" class=ISymbol>MEM_METHOD</a></td></tr><tr><td class=IHeading><a name="S"></a>S</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#SEED_METHOD" id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')" class=ISymbol>SEED_METHOD</a></td></tr><tr><td class=IHeading><a name="T"></a>T</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#TOTAL_DATA_SIZE" id=link6 onMouseOver="ShowTip(event, 'tt6', 'link6')" onMouseOut="HideTip('tt6')" class=ISymbol>TOTAL_DATA_SIZE</a></td></tr></table>
<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CConfiguration>Define to 1 if the platform supports floating point.</div></div><div class=CToolTip id="tt2"><div class=CConfiguration>Define to 1 if the platform has stdio.h and implements the printf function.</div></div><div class=CToolTip id="tt3"><div class=CConfiguration>Define to 1 if platform has the time.h header file, and implementation of functions thereof.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt4"><div class=CConfiguration>Defines method to get a block of memry. </div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt5"><div class=CConfiguration>Defines method to get seed values that cannot be computed at compile time. </div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt6"><div class=CConfiguration>Define total size for data algorithms will operate on</div></div><!--END_ND_TOOLTIPS-->

</div><!--Index-->


<div id=Footer><a href="http://www.naturaldocs.org">Generated by Natural Docs</a></div><!--Footer-->


<div id=Menu><div class=MEntry><div class=MFile><a href="../files/readme-txt.html">CoreMark Version 0.1a</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_list-c.html">core_list.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_portme-c.html">core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_main-c.html">core_results</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Index</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MIndex><a href="General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="Classes.html">Classes</a></div></div><div class=MEntry><div class=MIndex><a href="Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="Types.html">Types</a></div></div><div class=MEntry><div class=MIndex id=MSelected>Configurations</div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="Classes">Classes</option><option value="Configurations">Configurations</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option></select></div></div><!--Menu-->


<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>