# SPDX-FileCopyrightText: 2021 Espressif Systems (Shanghai) CO LTD
#
# SPDX-License-Identifier: Apache-2.0

# ATTENTION:
# This configuration file targets the building for CI environment and contains
# a set of definitions to resemble a bootloader image for RELEASE environment.
# Running the generated firmware image may result in irreversible operations
# to the chip!

CONFIG_SECURE_SIGNED_ON_BOOT=1
CONFIG_SECURE_SIGNED_APPS_RSA_SCHEME=1
CONFIG_SECURE_BOOT=1
CONFIG_SECURE_BOOT_V2_ENABLED=1
CONFIG_SECURE_BOOT_SUPPORTS_RSA=1
CONFIG_SECURE_FLASH_ENC_ENABLED=1
CONFIG_SECURE_FLASH_ENCRYPTION_MODE_RELEASE=1
CONFIG_ESP_SIGN_KEY_FILE=root-ed25519.pem
CONFIG_ESP_USE_TINYCRYPT=1
CONFIG_ESP_SIGN_ED25519=1
CONFIG_ESP_DOWNGRADE_PREVENTION=1
CONFIG_ESP_DOWNGRADE_PREVENTION_SECURITY_COUNTER=1
