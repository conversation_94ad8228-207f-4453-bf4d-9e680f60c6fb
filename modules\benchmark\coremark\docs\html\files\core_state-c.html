<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>/cygdrive/d/dev/code/coremark/core_state.c - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CFile"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="core_state.c"></a>core_state.c</h1><div class=CBody><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#core_state.c" >core_state.c</a></td><td class=SDescription></td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Description" >Description</a></td><td class=SDescription>Simple state machines like this one are used in many embedded products.</td></tr><tr class="SGroup"><td class=SEntry><a href="#Functions" >Functions</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#core_bench_state" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">core_bench_state</a></td><td class=SDescription>Benchmark function</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#core_init_state" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')">core_init_state</a></td><td class=SDescription>Initialize the input data for the state machine.</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#core_state_transition" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')">core_state_transition</a></td><td class=SDescription>Actual state machine.</td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Description"></a>Description</h3><div class=CBody><p>Simple state machines like this one are used in many embedded products.</p><p>For more complex state machines, sometimes a state transition table implementation is used instead, trading speed of direct coding for ease of maintenance.</p><p>Since the main goal of using a state machine in CoreMark is to excercise the switch/if behaviour, we are using a small moore machine.</p><p>In particular, this machine tests type of string input, trying to determine whether the input is a number or something else.&nbsp; <a href="#Image1" class=CImageLink>(see core_state)</a>.</p><blockquote><div class=CImage><a name="Image1"></a><div class=CImageCaption>core_state</div><img src="docs/core_state.png" width="768" height="570"></div></blockquote></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="Functions"></a>Functions</h3></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_bench_state"></a>core_bench_state</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_u16 core_bench_state(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>blksize,</td></tr><tr><td></td><td class=PType nowrap>ee_u8&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed1,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed2,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>step,</td></tr><tr><td></td><td class=PType nowrap>ee_u16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>crc</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Benchmark function</p><p>Go over the input twice, once direct, and once after introducing some corruption.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_init_state"></a>core_init_state</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void core_init_state(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>size,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed,</td></tr><tr><td></td><td class=PType nowrap>ee_u8&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Initialize the input data for the state machine.</p><p>Populate the input with several predetermined strings, interspersed.&nbsp; Actual patterns chosen depend on the seed parameter.</p><h4 class=CHeading>Note</h4><p>The seed parameter MUST be supplied from a source that cannot be determined at compile time</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_state_transition"></a>core_state_transition</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>enum CORE_STATE core_state_transition(</td><td class=PTypePrefix nowrap>ee_u8&nbsp;</td><td class=PType nowrap>**instr&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>transition_count</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Actual state machine.</p><h4 class=CHeading>The state machine will continue scanning until either</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>1</td><td class=CDLDescription>an invalid input is detcted.</td></tr><tr><td class=CDLEntry>2</td><td class=CDLDescription>a valid number has been detected.</td></tr></table><p>The input pointer is updated to point to the end of the token, and the end state is returned (either specific format determined or invalid).</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile id=MSelected>core_state.c</div></div><div class=MEntry><div class=MFile><a href="core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="../index/BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_u16 core_bench_state(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>blksize,</td></tr><tr><td></td><td class=PType nowrap>ee_u8&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed1,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed2,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>step,</td></tr><tr><td></td><td class=PType nowrap>ee_u16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>crc</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Benchmark function</div></div><div class=CToolTip id="tt2"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void core_init_state(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>size,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed,</td></tr><tr><td></td><td class=PType nowrap>ee_u8&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Initialize the input data for the state machine.</div></div><div class=CToolTip id="tt3"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>enum CORE_STATE core_state_transition(</td><td class=PTypePrefix nowrap>ee_u8&nbsp;</td><td class=PType nowrap>**instr&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>transition_count</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Actual state machine.</div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>