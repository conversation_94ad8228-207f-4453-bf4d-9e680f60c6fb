# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BOOT_SERIAL_DETECT_PIN:
        description: >
            Start the serial boot loader if this pin is asserted at boot time.
        value: '-1'
        restrictions:
            - '(BOOT_SERIAL_DETECT_PIN != -1) ||
               (BOOT_SERIAL_DETECT_TIMEOUT != 0) ||
               (BOOT_SERIAL_NVREG_INDEX != -1)'

    BOOT_SERIAL_DETECT_PIN_CFG:
        description: >
            GPIO configuration for the serial boot loader detect pin.
        value: 'HAL_GPIO_PULL_UP'

    BOOT_SERIAL_DETECT_PIN_VAL:
        description: >
            The value the detect pin must be set to for the serial boot loader
            to start.
        value: 0

    BOOT_SERIAL_DETECT_TIMEOUT:
        description: >
            The duration, in milliseconds, to listen on the UART for the
            management string (BOOT_SERIAL_DETECT_STRING).  If the management
            string is detected during this period, the serial boot loader is
            started.  If the period expires without the management string being
            received, the boot loader runs in the normal (non-serial) mode.
            Specify 0 to disable listening on the UART for the management
            string.
        value: 0
        restrictions:
            - '(BOOT_SERIAL_DETECT_PIN != -1) ||
               (BOOT_SERIAL_DETECT_TIMEOUT != 0) ||
               (BOOT_SERIAL_NVREG_INDEX != -1)'

    BOOT_SERIAL_DETECT_STRING:
        description: >
            The string to listen for on the UART.  If this management string is
            detected during the timeout period, the serial boot loader is
            started.  If the period expires without this string being received,
            the boot loader runs in the normal (non-serial) mode.  This setting
            has no effect if BOOT_SERIAL_DETECT_TIMEOUT is set to 0.
        value: '"nmgr"'

    BOOT_SERIAL_REPORT_PIN:
        description: >
            The GPIO to toggle while the serial boot loader is running.  Set to
            -1 to disable reporting.
        value: 'LED_BLINK_PIN'

    BOOT_SERIAL_REPORT_FREQ:
        description: >
            The toggle rate, in Hz, of the serial boot loader report pin.
        value: 4

    BOOT_SERIAL_NVREG_MAGIC:
        description: >
            Magic number, to be saved in a retained (reset-surviving) register.
            If the value in the register matches, the serial bootloader will
            load. Value must not be 0.
        value: 0xB7
        restrictions:
            - '(BOOT_SERIAL_NVREG_MAGIC != 0)'

    BOOT_SERIAL_NVREG_INDEX:
        description: >
            Index of retained register to use (using hal_nvreg_read) for reading
            magic value.
        value: -1
        restrictions:
            - '(BOOT_SERIAL_DETECT_PIN != -1) ||
               (BOOT_SERIAL_DETECT_TIMEOUT != 0) ||
               (BOOT_SERIAL_NVREG_INDEX != -1)'

    BOOT_SERIAL_MGMT_ECHO:
        description: If enabled, support for the mcumgr echo command is being added.
        value: 0
