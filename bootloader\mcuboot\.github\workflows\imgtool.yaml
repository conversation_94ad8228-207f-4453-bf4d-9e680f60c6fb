on:
  push:
    branches:
      - main
      - v*-branch
  pull_request:

name: imgtool

concurrency:
  group: imgtool-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.x", "pypy3.9"]
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pipenv'
        cache-dependency-path: |
          scripts/setup.py
    - name: Install dependencies
      run: |
        pip install --user pipenv
    - name: Run tests
      run: |
        cd scripts
        pipenv run pip install pytest -e .
        pipenv run pytest --junitxml=../junit/pytest-results-${{ matrix.python-version }}.xml
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: pytest-results-${{ matrix.python-version }}
        path: |
          junit/pytest-results-${{ matrix.python-version }}*.xml
        if-no-files-found: ignore
  environment:
    if: ${{ github.event_name == 'push' }}
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - name: Cache pip
      uses: actions/cache@v1
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip
    - name: Install packages
      run: |
        export PATH="$HOME/.local/bin:$PATH"
        ./ci/imgtool_install.sh
    - name: Publish
      env:
        TWINE_TOKEN: ${{ secrets.TWINE_TOKEN }}
      run: |
        export PATH="$HOME/.local/bin:$PATH"
        ./ci/imgtool_run.sh
