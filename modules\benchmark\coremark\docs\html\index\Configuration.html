<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>Configuration Index - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="IndexPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=IPageTitle>Configuration Index</div><div class=INavigationBar>$#! &middot; 0-9 &middot; A &middot; B &middot; <a href="#C">C</a> &middot; D &middot; E &middot; F &middot; G &middot; <a href="#H">H</a> &middot; I &middot; J &middot; K &middot; L &middot; <a href="#M">M</a> &middot; N &middot; O &middot; P &middot; Q &middot; R &middot; <a href="#S">S</a> &middot; <a href="#T">T</a> &middot; <a href="#U">U</a> &middot; V &middot; W &middot; X &middot; Y &middot; Z</div><table border=0 cellspacing=0 cellpadding=0><tr><td class=IHeading id=IFirstHeading><a name="C"></a>C</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#CORE_TICKS" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')" class=ISymbol>CORE_TICKS</a></td></tr><tr><td class=IHeading><a name="H"></a>H</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#HAS_FLOAT" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')" class=ISymbol>HAS_FLOAT</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#HAS_PRINTF" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')" class=ISymbol>HAS_PRINTF</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#HAS_STDIO" id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')" class=ISymbol>HAS_STDIO</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#HAS_TIME_H" id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')" class=ISymbol>HAS_TIME_H</a></td></tr><tr><td class=IHeading><a name="M"></a>M</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#MAIN_HAS_NOARGC" id=link6 onMouseOver="ShowTip(event, 'tt6', 'link6')" onMouseOut="HideTip('tt6')" class=ISymbol>MAIN_HAS_NOARGC</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#MAIN_HAS_NORETURN" id=link7 onMouseOver="ShowTip(event, 'tt7', 'link7')" onMouseOut="HideTip('tt7')" class=ISymbol>MAIN_HAS_NORETURN</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#MEM_METHOD" id=link8 onMouseOver="ShowTip(event, 'tt8', 'link8')" onMouseOut="HideTip('tt8')" class=ISymbol>MEM_METHOD</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#MULTITHREAD" id=link9 onMouseOver="ShowTip(event, 'tt9', 'link9')" onMouseOut="HideTip('tt9')" class=ISymbol>MULTITHREAD</a></td></tr><tr><td class=IHeading><a name="S"></a>S</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#SEED_METHOD" id=link10 onMouseOver="ShowTip(event, 'tt10', 'link10')" onMouseOut="HideTip('tt10')" class=ISymbol>SEED_METHOD</a></td></tr><tr><td class=IHeading><a name="T"></a>T</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#TOTAL_DATA_SIZE" id=link11 onMouseOver="ShowTip(event, 'tt11', 'link11')" onMouseOut="HideTip('tt11')" class=ISymbol>TOTAL_DATA_SIZE</a></td></tr><tr><td class=IHeading><a name="U"></a>U</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#USE_CLOCK" id=link12 onMouseOver="ShowTip(event, 'tt12', 'link12')" onMouseOut="HideTip('tt12')" class=ISymbol>USE_CLOCK</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#USE_FORK" id=link13 onMouseOver="ShowTip(event, 'tt13', 'link13')" onMouseOut="HideTip('tt13')" class=ISymbol>USE_FORK</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#USE_PTHREAD" id=link14 onMouseOver="ShowTip(event, 'tt14', 'link14')" onMouseOut="HideTip('tt14')" class=ISymbol>USE_PTHREAD</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#USE_SOCKET" id=link15 onMouseOver="ShowTip(event, 'tt15', 'link15')" onMouseOut="HideTip('tt15')" class=ISymbol>USE_SOCKET</a></td></tr></table>
<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CConfiguration>Define type of return from the timing functions.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt2"><div class=CConfiguration>Define to 1 if the platform supports floating point.</div></div><div class=CToolTip id="tt3"><div class=CConfiguration>Define to 1 if the platform has stdio.h and implements the printf function.</div></div><div class=CToolTip id="tt4"><div class=CConfiguration>Define to 1 if the platform has stdio.h.</div></div><div class=CToolTip id="tt5"><div class=CConfiguration>Define to 1 if platform has the time.h header file, and implementation of functions thereof.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt6"><div class=CConfiguration>Needed if platform does not support getting arguments to main.</div></div><div class=CToolTip id="tt7"><div class=CConfiguration>Needed if platform does not support returning a value from main.</div></div><div class=CToolTip id="tt8"><div class=CConfiguration>Defines method to get a block of memry.</div></div><div class=CToolTip id="tt9"><div class=CConfiguration>Define for parallel execution</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt10"><div class=CConfiguration>Defines method to get seed values that cannot be computed at compile time.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt11"><div class=CConfiguration>Define total size for data algorithms will operate on</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt12"><div class=CConfiguration>Define to 1 if platform has the time.h header file, and implementation of functions thereof.</div></div><div class=CToolTip id="tt13"><div class=CConfiguration>Sample implementation for launching parallel contexts This implementation uses fork, waitpid, shmget,shmat and shmdt.</div></div><div class=CToolTip id="tt14"><div class=CConfiguration>Sample implementation for launching parallel contexts This implementation uses pthread_thread_create and pthread_join.</div></div><div class=CToolTip id="tt15"><div class=CConfiguration>Sample implementation for launching parallel contexts This implementation uses fork, socket, sendto and recvfrom</div></div><!--END_ND_TOOLTIPS-->

</div><!--Index-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="../files/readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="../files/release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../files/core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="Types.html">Types</a></div></div><div class=MEntry><div class=MIndex id=MSelected>Configurations</div></div><div class=MEntry><div class=MIndex><a href="Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->


<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>