# Copyright (c) 2017-2020 Linaro Limited
# Copyright (c) 2020 Arm Limited
# Copyright (c) 2023 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: Apache-2.0
#

mainmenu "MCUboot configuration"

comment "MCUboot-specific configuration options"

source "$(ZEPHYR_NRF_MODULE_DIR)/modules/mcuboot/boot/zephyr/Kconfig"

# Hidden option to mark a project as MCUboot
config MCUBOOT
	default y
	bool
	select MPU_ALLOW_FLASH_WRITE if <PERSON><PERSON>_<PERSON><PERSON>
	select USE_DT_CODE_PARTITION if HAS_FLASH_LOAD_OFFSET
	select MCUBOOT_BOOTUTIL_LIB
	select REBOOT if SECURE_BOOT

config BOOT_USE_MBEDTLS
	bool
	# Hidden option
	default n
	help
	  Use mbedTLS for crypto primitives.

config BOOT_USE_PSA_CRYPTO
	bool
	default y if NRF_SECURITY
	# This is counter intuitive but that is how PSA heap is enabled.
	select MBEDTLS_ENABLE_HEAP
	select MBEDTLS_PSA_CRYPTO_C
	help
	  Hidden option set if using PSA crypt for cryptography functionality

config BOOT_USE_TINYCRYPT
	bool
	# Hidden option
	default n
	# When building for ECDSA, we use our own copy of mbedTLS, so the
	# Zephyr one must not be enabled or the MBEDTLS_CONFIG_FILE macros
	# will collide.
	select MBEDTLS_PROMPTLESS if ZEPHYR_MBEDTLS_MODULE
	help
	  Use TinyCrypt for crypto primitives.

config BOOT_USE_CC310
	bool
	# Hidden option
	default n
	# When building for ECDSA, we use our own copy of mbedTLS, so the
	# Zephyr one must not be enabled or the MBEDTLS_CONFIG_FILE macros
	# will collide.
	help
	  Use cc310 for crypto primitives.

config BOOT_USE_NRF_CC310_BL
	bool
	default n

config NRFXLIB_CRYPTO
	bool
	default n

config NRF_CC310_BL
	bool
	default n

if BOOT_USE_PSA_CRYPTO

config BOOT_PSA_IMG_HASH_ALG_SHA256_DEPENDENCIES
	bool
	default y if BOOT_IMG_HASH_ALG_SHA256
	select PSA_WANT_ALG_SHA_256
	help
	  Dependencies for hashing with SHA256

config BOOT_ED25519_PSA_DEPENDENCIES
	bool
	select PSA_WANT_ALG_SHA_256 if BOOT_IMG_HASH_ALG_SHA256
	select PSA_WANT_ALG_SHA_512
	select PSA_WANT_ALG_PURE_EDDSA
	select PSA_WANT_ECC_TWISTED_EDWARDS_255
	select PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT
	help
	  Dependencies for ed25519 signature

if BOOT_ENCRYPT_IMAGE

config BOOT_X25519_PSA_DEPENDENCIES
	bool
	select PSA_WANT_ALG_ECDH
	select PSA_WANT_ALG_HMAC
	select PSA_WANT_ALG_HKDF
	select PSA_WANT_ALG_CTR
	select PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT
	select PSA_WANT_KEY_TYPE_DERIVE
	select PSA_WANT_KEY_TYPE_AES
	select PSA_WANT_ECC_MONTGOMERY_255
	help
	  Dependencies for x25519 shared-random key encryption and AES
	  encryption. The PSA_WANT_ALG_CTR and PSA_WANT_KEY_TYPE_AES
	  enable Counter based block cipher and AES key, and algorithm support,
	  to use with it; the others are used for shared key decryption
	  and derivation.

endif # BOOT_ENCRYPT_IMAGE

if MBEDTLS_ENABLE_HEAP

config MBEDTLS_HEAP_SIZE
	default 2048 if BOOT_USE_PSA_CRYPTO
	help
	  The PSA internals need to be able to allocate memory for operation
	  and it uses mbedTLS heap for that.

endif # MBEDTLS_ENABLE_HEAP

endif # BOOT_USE_PSA_CRYPTO

menu "MCUBoot settings"

config SINGLE_APPLICATION_SLOT
	bool "Single slot bootloader"
	default n
	help
	  Single image area is used for application which means that
	  uploading a new application overwrites the one that previously
	  occupied the area.

config BOOT_IMG_HASH_ALG_SHA256_ALLOW
	bool
	help
	  Hidden option set by configurations that allow SHA256

config BOOT_IMG_HASH_ALG_SHA384_ALLOW
	bool
	help
	  Hidden option set by configurations that allow SHA384

config BOOT_IMG_HASH_ALG_SHA512_ALLOW
	bool
	help
	  Hidden option set by configurations that allow SHA512

config BOOT_IMG_HASH_DIRECTLY_ON_STORAGE
	bool "Hash calculation functions access storage through address space"
	depends on !BOOT_ENCRYPT_IMAGE
	help
	  When possible to map storage device, at least for read operations,
	  to address space or RAM area, enabling this option allows hash
	  calculation functions to directly access the storage through that address
	  space or using its own DMA. This reduces flash read overhead done
	  by the MCUboot.
	  Notes:
	    - not supported when encrypted images are in use, because calculating
	      SHA requires image to be decrypted first, which is done to RAM.
	    - currently only supported on internal storage of devices; this
	      option will not work with devices that use external storage for
	      either of image slots.

choice BOOT_IMG_HASH_ALG
	prompt "Selected image hash algorithm"
	default BOOT_IMG_HASH_ALG_SHA256 if BOOT_IMG_HASH_ALG_SHA256_ALLOW
	default BOOT_IMG_HASH_ALG_SHA384 if BOOT_IMG_HASH_ALG_SHA384_ALLOW
	default BOOT_IMG_HASH_ALG_SHA512 if BOOT_IMG_HASH_ALG_SHA512_ALLOW
	help
	  Hash algorithm used for image verification. Selection
	  here may be limited by other configurations, like for
	  example selected cryptographic signature.

config BOOT_IMG_HASH_ALG_SHA256
	bool "SHA256"
	depends on BOOT_IMG_HASH_ALG_SHA256_ALLOW
	help
	  SHA256 algorithm

config BOOT_IMG_HASH_ALG_SHA384
	bool "SHA384"
	depends on BOOT_IMG_HASH_ALG_SHA384_ALLOW
	help
	  SHA384 algorithm

config BOOT_IMG_HASH_ALG_SHA512
	bool "SHA512"
	depends on BOOT_IMG_HASH_ALG_SHA512_ALLOW
	help
	  SHA512 algorithm

endchoice # BOOT_IMG_HASH_ALG

config BOOT_SIGNATURE_TYPE_PURE_ALLOW
	bool
	help
	  Hidden option set by configurations that allow Pure variant,
	  for example ed25519. The pure variant means that image
	  signature is calculated over entire image instead of hash
	  of an image.

choice BOOT_SIGNATURE_TYPE
	prompt "Signature type"
	default BOOT_SIGNATURE_TYPE_ED25519 if SOC_NRF54L15_CPUAPP
	default BOOT_SIGNATURE_TYPE_RSA

config BOOT_SIGNATURE_TYPE_NONE
	bool "No signature; use only hash check"
	select BOOT_USE_TINYCRYPT
	select BOOT_IMG_HASH_ALG_SHA256_ALLOW

config BOOT_SIGNATURE_TYPE_RSA
	bool "RSA signatures"
	select BOOT_USE_MBEDTLS
	select MBEDTLS
	select BOOT_ENCRYPTION_SUPPORT
	select BOOT_IMG_HASH_ALG_SHA256_ALLOW

if BOOT_SIGNATURE_TYPE_RSA
config BOOT_SIGNATURE_TYPE_RSA_LEN
	int "RSA signature length"
	range 2048 3072
	default 2048
endif

config BOOT_SIGNATURE_TYPE_ECDSA_P256
	bool "Elliptic curve digital signatures with curve P-256"
	select BOOT_ENCRYPTION_SUPPORT
	select BOOT_IMG_HASH_ALG_SHA256_ALLOW

if BOOT_SIGNATURE_TYPE_ECDSA_P256
choice BOOT_ECDSA_IMPLEMENTATION
	prompt "Ecdsa implementation"
	default BOOT_ECDSA_TINYCRYPT

config BOOT_ECDSA_TINYCRYPT
	bool "Use tinycrypt"
	select BOOT_USE_TINYCRYPT

config BOOT_ECDSA_CC310
	bool "Use CC310"
	depends on HAS_HW_NRF_CC310
	select BOOT_USE_NRF_CC310_BL
	select NRF_CC310_BL
	select NRFXLIB_CRYPTO
	select BOOT_USE_CC310
endchoice # Ecdsa implementation
endif

config BOOT_SIGNATURE_TYPE_ED25519
	bool "Edwards curve digital signatures using ed25519"
	select BOOT_ENCRYPTION_SUPPORT if !BOOT_SIGNATURE_TYPE_PURE
	select BOOT_IMG_HASH_ALG_SHA256_ALLOW if !BOOT_SIGNATURE_TYPE_PURE
	# The SHA is used only for key hashing, not for images.
	select BOOT_IMG_HASH_ALG_SHA512_ALLOW if BOOT_USE_PSA_CRYPTO
	select BOOT_SIGNATURE_TYPE_PURE_ALLOW
	help
	  This is ed25519 signature calculated over SHA512 of SHA256 of application
	  image; that is not completely correct approach as the SHA512 should be
	  rather directly calculated over an image.
	  Select BOOT_SIGNATURE_TYPE_PURE to have a PureEdDSA calculating image
	  signature directly on image, rather than hash of the image.

if BOOT_SIGNATURE_TYPE_ED25519

config BOOT_SIGNATURE_TYPE_PURE
	bool "Use Pure signature of image"
	depends on BOOT_SIGNATURE_TYPE_PURE_ALLOW
	help
	  The Pure signature is calculated directly over image rather than
	  hash of an image.
	  This is more secure signature, specifically if hardware can do the
	  verification without need to share key.
	  Note that this requires that all slots for which signature is to be
	  verified need to be accessible through memory address space that
	  cryptography can access.

choice BOOT_ED25519_IMPLEMENTATION
	prompt "Ecdsa implementation"
	default BOOT_ED25519_TINYCRYPT

config BOOT_ED25519_TINYCRYPT
	bool "Use tinycrypt"
	select BOOT_USE_TINYCRYPT
	depends on !NRF_SECURITY

config BOOT_ED25519_MBEDTLS
	bool "Use mbedTLS"
	select BOOT_USE_MBEDTLS
	select MBEDTLS
	depends on !NRF_SECURITY

config BOOT_ED25519_PSA
	bool "Use PSA crypto"
	depends on NRF_SECURITY
	select BOOT_USE_PSA_CRYPTO
	select BOOT_ED25519_PSA_DEPENDENCIES
	select BOOT_X25519_PSA_DEPENDENCIES if BOOT_ENCRYPT_IMAGE

endchoice
endif

endchoice

config BOOT_SIGNATURE_USING_KMU
	bool "Use KMU stored keys for signature verification"
	depends on NRF_SECURITY
	depends on CRACEN_LIB_KMU
	select PSA_WANT_ALG_GCM
	select PSA_WANT_KEY_TYPE_AES
	select PSA_WANT_AES_KEY_SIZE_256
	select PSA_WANT_ALG_SP800_108_COUNTER_CMAC
	select PSA_WANT_ALG_CMAC
	select PSA_WANT_ALG_ECB_NO_PADDING
	help
	  MCUboot will use keys provisioned to the device key management unit for signature
	  verification instead of compiling in key data from a file.

if BOOT_SIGNATURE_USING_KMU

config BOOT_SIGNATURE_KMU_SLOTS
	int "KMU key slots"
	range 1 3
	default 1
	help
	  Selects the number of KMU key slots (also known as generations) to use when verifying
	  an image.

endif

if !BOOT_SIGNATURE_USING_KMU

config BOOT_SIGNATURE_KEY_FILE
	string "PEM key file"
	default "root-ec-p256.pem" if BOOT_SIGNATURE_TYPE_ECDSA_P256
	default "root-ed25519.pem" if BOOT_SIGNATURE_TYPE_ED25519
	default "root-rsa-3072.pem" if BOOT_SIGNATURE_TYPE_RSA && BOOT_SIGNATURE_TYPE_RSA_LEN=3072
	default "root-rsa-2048.pem" if BOOT_SIGNATURE_TYPE_RSA && BOOT_SIGNATURE_TYPE_RSA_LEN=2048
	default ""
	help
	  You can use either absolute or relative path.
	  In case relative path is used, the build system assumes that it starts
	  from the directory where the MCUBoot KConfig configuration file is
	  located. If the key file is not there, the build system uses relative
	  path that starts from the MCUBoot repository root directory.
	  The key file will be parsed by imgtool's getpub command and a .c source
	  with the public key information will be written in a format expected by
	  MCUboot.

endif

config MCUBOOT_CLEANUP_ARM_CORE
	bool "Perform core cleanup before chain-load the application"
	depends on CPU_CORTEX_M
	default y
	help
	  This option instructs MCUboot to perform a clean-up of a set of
	  architecture core HW registers before jumping to the application
	  firmware. The clean-up sets these registers to their warm-reset
	  values as specified by the architecture.

	  This option is enabled by default to prevent possible problems when
	  booting zephyr (or other) applications whereby e.g. a MPU stack guard
	  may be initialised in RAM which is then used by the application
	  start-up code which can cause a module fault and potentially make the
	  module irrecoverable.

config MCUBOOT_CLEANUP_RAM
	bool "Perform RAM cleanup"
	depends on CPU_CORTEX_M4 || CPU_CORTEX_M33
	help
	  Sets contents of memory to 0 before jumping to application.

# Disable MBEDTLS from being selected if NRF_SECURITY is enabled, and use default NRF_SECURITY
# configuration file for MBEDTLS
config MBEDTLS
	depends on !NRF_SECURITY

config NRF_SECURITY
	select MBEDTLS_PROMPTLESS

if MBEDTLS

config MBEDTLS_CFG_FILE
	default "mcuboot-mbedtls-cfg.h" if !NRF_SECURITY

endif

config BOOT_HW_KEY
	bool "Use HW key for image verification"
	default n
	help
	  Use HW key for image verification, otherwise the public key is embedded
	  in MCUBoot. If enabled the public key is appended to the signed image
	  and requires the hash of the public key to be provisioned to the device
	  beforehand.

config BOOT_VALIDATE_SLOT0
	bool "Validate image in the primary slot on every boot"
	default y
	help
	  If y, the bootloader attempts to validate the signature of the
	  primary slot every boot. This adds the signature check time to
	  every boot, but can mitigate against some changes that are
	  able to modify the flash image itself.

config BOOT_VALIDATE_SLOT0_ONCE
	bool "Validate image in the primary slot just once after after upgrade"
	depends on !BOOT_VALIDATE_SLOT0 && SINGLE_APPLICATION_SLOT
	default n
	help
	  If y, the bootloader attempts to validate the signature of the
	  primary slot only once after an upgrade of the main slot.
	  It caches the result in the magic area, which makes it an unsecure
	  method. This option is usefull for lowering the boot up time for
	  low end devices with as a compromise lowering the security level.
	  If unsure, leave at the default value.

config BOOT_PREFER_SWAP_MOVE
	bool "Prefer the newer swap move algorithm"
	default y if SOC_FAMILY_NORDIC_NRF
	default y if !$(dt_nodelabel_enabled,scratch_partition)
	help
	  If y, the BOOT_IMAGE_UPGRADE_MODE will default to using
	  "move" instead of "scratch".  This is a separate bool config
	  option, because Kconfig doesn't allow defaults to be
	  overridden in choice options.  Most devices should be using
	  swap move.

if !SINGLE_APPLICATION_SLOT
choice BOOT_IMAGE_UPGRADE_MODE
	prompt "Image upgrade modes"
	default BOOT_SWAP_USING_MOVE if BOOT_PREFER_SWAP_MOVE
	default BOOT_SWAP_USING_SCRATCH

config BOOT_SWAP_USING_SCRATCH
	bool "Swap mode that run with the scratch partition"
	help
	  This is the most conservative swap mode but it can work even on
	  devices with heterogeneous flash page layout.

config BOOT_UPGRADE_ONLY
	bool "Overwrite image updates instead of swapping"
	help
	  If y, overwrite the primary slot with the upgrade image instead
	  of swapping them. This prevents the fallback recovery, but
	  uses a much simpler code path.

config BOOT_SWAP_USING_MOVE
	bool "Swap mode that can run without a scratch partition"
	help
	  If y, the swap upgrade is done in two steps, where first every
	  sector of the primary slot is moved up one sector, then for
	  each sector X in the secondary slot, it is moved to index X in
	  the primary slot, then the sector at X+1 in the primary is
	  moved to index X in the secondary.
	  This allows a swap upgrade without using a scratch partition,
	  but is currently limited to all sectors in both slots being of
	  the same size.

config BOOT_DIRECT_XIP
	bool "Run the latest image directly from its slot"
	help
	  If y, mcuboot selects the newest valid image based on the image version
	  numbers, thereafter the selected image can run directly from its slot
	  without having to move/copy it into the primary slot. For this reason the
	  images must be linked to be executed from the given image slot. Using this
	  mode results in a simpler code path and smaller code size.

config BOOT_RAM_LOAD
	bool "RAM load"
	help
	  If y, mcuboot selects the newest valid image based on the image version
	  numbers, thereafter the selected image is copied to RAM and executed from
	  there. For this reason, the image has to be linked to be executed from RAM.
	  The address that the image is copied to is specified using the load-addr
	  argument to the imgtool.py script which writes it to the image header.

config BOOT_FIRMWARE_LOADER
	bool "Firmware loader"
	help
	  If y, mcuboot will have a single application slot, and the secondary
	  slot will be for a non-upgradeable firmware loaded image (e.g. for
	  loading firmware via Bluetooth). The main application will boot by
	  default unless there is an error with it or the boot mode has been
	  forced to the firmware loader.

	  Note: The firmware loader image must be signed with the same signing
	  key as the primary image.

endchoice

# Workaround for not being able to have commas in macro arguments
DT_CHOSEN_Z_SRAM := zephyr,sram

if BOOT_RAM_LOAD
config BOOT_IMAGE_EXECUTABLE_RAM_START
	hex "Boot image executable ram start"
	default $(dt_chosen_reg_addr_hex,$(DT_CHOSEN_Z_SRAM))

config BOOT_IMAGE_EXECUTABLE_RAM_SIZE
	int "Boot image executable base size"
	default $(dt_chosen_reg_size_int,$(DT_CHOSEN_Z_SRAM),0)
endif

config BOOT_DIRECT_XIP_REVERT
	bool "Enable the revert mechanism in direct-xip mode"
	depends on BOOT_DIRECT_XIP
	default n
	help
	  If y, enables the revert mechanism in direct-xip similar to the one in
	  swap mode. It requires the trailer magic to be added to the signed image.
	  When a reboot happens without the image being confirmed at runtime, the
	  bootloader considers the image faulty and erases it. After this it will
	  attempt to boot the previous image. The images can also be made permanent
	  (marked as confirmed in advance) just like in swap mode.

config BOOT_BOOTSTRAP
	bool "Bootstrap erased the primary slot from the secondary slot"
	default n
	help
	  If y, enables bootstraping support. Bootstrapping allows an erased
	  primary slot to be initialized from a valid image in the secondary slot.
	  If unsure, leave at the default value.

config BOOT_SWAP_SAVE_ENCTLV
	bool "Save encrypted key TLVs instead of plaintext keys in swap metadata"
	default n
	depends on BOOT_ENCRYPT_IMAGE
	help
	  If y, instead of saving the encrypted image keys in plaintext in the
	  swap resume metadata, save the encrypted image TLVs. This should be used
	  when there is no security mechanism protecting the data in the primary
	  slot from being dumped. If n is selected (default), the keys are written
	  after being decrypted from the image TLVs and could be read by an
	  attacker who has access to the flash contents of the primary slot (eg
	  JTAG/SWD or primary slot in external flash).
	  If unsure, leave at the default value.

endif # !SINGLE_APPLICATION_SLOT

config BOOT_ENCRYPTION_SUPPORT
	bool
	help
	  Hidden option used to check if image encryption is supported.

config BOOT_ENCRYPT_IMAGE
	bool "Support for encrypted image updates"
	depends on BOOT_ENCRYPTION_SUPPORT
	select BOOT_ENCRYPT_RSA if BOOT_SIGNATURE_TYPE_RSA
	select BOOT_ENCRYPT_EC256 if BOOT_SIGNATURE_TYPE_ECDSA_P256
	select BOOT_ENCRYPT_X25519 if BOOT_SIGNATURE_TYPE_ED25519
	depends on !SINGLE_APPLICATION_SLOT || MCUBOOT_SERIAL
	help
	  If y, images in the secondary slot can be encrypted and are decrypted
	  on the fly when upgrading to the primary slot, as well as encrypted
	  back when swapping from the primary slot to the secondary slot. The
	  encryption mechanism must match the same type as the signature type,
	  supported types include:
	   - RSA-OAEP (2048 bits).
	   - ECIES using primitives described under "ECIES-P256 encryption" in
	     docs/encrypted_images.md.
	   - ECIES using primitives described under "ECIES-X25519 encryption"
	     in docs/encrypted_images.md.

	  Note that for single slot operation, this can still be used to allow
	  loading encrypted images via serial recovery which are then
	  decrypted on-the-fly without needing a second slot.

config BOOT_ENCRYPT_RSA
	bool
	help
	  Hidden option selecting RSA encryption.

config BOOT_ENCRYPT_EC256
	bool
	help
	  Hidden option selecting EC256 encryption.

config BOOT_ENCRYPT_X25519
	bool
	help
	  Hidden option selecting x25519 encryption.

config BOOT_ENCRYPTION_KEY_FILE
	string "Encryption key file"
	depends on BOOT_ENCRYPT_IMAGE
	default "enc-rsa2048-priv.pem" if BOOT_ENCRYPT_RSA
	default "enc-ec256-priv.pem" if BOOT_ENCRYPT_EC256
	default "enc-x25519-priv.pem" if BOOT_ENCRYPT_X25519
	default ""
	help
	  You can use either absolute or relative path.
	  In case relative path is used, the build system assumes that it starts
	  from the directory where the MCUBoot KConfig configuration file is
	  located. If the key file is not there, the build system uses relative
	  path that starts from the MCUBoot repository root directory.
	  The key file will be parsed by imgtool's getpriv command and a .c source
	  with the public key information will be written in a format expected by
	  MCUboot.

config BOOT_MAX_IMG_SECTORS_AUTO
	bool "Calculate maximum sectors automatically"
	default y if !PARTITION_MANAGER_ENABLED
	help
	  If this option is enabled then the maximum number of supported sectors per image will
	  be calculated automatically from the flash erase sizes and size of each partition for
	  the first image.

	  If this information is not available, or multiple images are used, then this option
	  should be disabled and BOOT_MAX_IMG_SECTORS should be set instead

config BOOT_MAX_IMG_SECTORS
	int "Maximum number of sectors per image slot"
	default 128
	depends on !BOOT_MAX_IMG_SECTORS_AUTO
	help
	  This option controls the maximum number of sectors that each of
	  the two image areas can contain. Smaller values reduce MCUboot's
	  memory usage; larger values allow it to support larger images.
	  If unsure, leave at the default value.

config BOOT_SHARE_BACKEND_AVAILABLE
	bool
	default n
	help
	  Hidden open which indicates if there is a sharing backend available.

# Workaround for not being able to have commas in macro arguments
DT_CHOSEN_BOOTLOADER_INFO := zephyr,bootloader-info

config BOOT_SHARE_BACKEND_AVAILABLE
	bool
	default n
	help
	  Hidden open which indicates if there is a sharing backend available.

choice BOOT_SHARE_BACKEND
	prompt "Shared data backend"
	default BOOT_SHARE_BACKEND_DISABLED

config BOOT_SHARE_BACKEND_DISABLED
	bool "Disabled"
	help
	  No data sharing support.

config BOOT_SHARE_BACKEND_RETENTION
	bool "Retention"
	depends on RETENTION
	depends on $(dt_chosen_enabled,$(DT_CHOSEN_BOOTLOADER_INFO))
	select BOOT_SHARE_BACKEND_AVAILABLE
	help
	  Use retention to share data with application. Requires:
	    - Retained memory area
	    - Retention partition of retained memory area
	    - Chosen node "zephyr,bootloader-info" to be set to the retention
	      partition

config BOOT_SHARE_BACKEND_EXTERNAL
	bool "External (user-provided code)"
	select BOOT_SHARE_BACKEND_AVAILABLE
	help
	  Use a custom user-specified storage.

endchoice

menuconfig BOOT_SHARE_DATA
	bool "Save application specific data"
	default n
	depends on BOOT_SHARE_BACKEND_AVAILABLE
	help
	  This will allow data to be shared between MCUboot and an application,
	  it does not include any informatiom by default.

	  Note: This requires a backend to function, see
	  BOOT_SHARE_BACKEND_RETENTION for details on using the retention
	  subsystem as a backend.

config BOOT_SHARE_DATA_BOOTINFO
	bool "Save boot information data"
	default n
	depends on BOOT_SHARE_DATA
	help
	  This will place information about the MCUboot configuration and
	  running application into a shared memory area.

menuconfig MEASURED_BOOT
	bool "Store the boot state/measurements in shared memory area"
	default n
	depends on BOOT_SHARE_BACKEND_AVAILABLE
	help
	  If enabled, the bootloader will store certain boot measurements such as
	  the hash of the firmware image in a shared memory area. This data can
	  be used later by runtime services (e.g. by a device attestation service).

	  Note: This requires a backend to function, see
	  BOOT_SHARE_BACKEND_RETENTION for details on using the retention
	  subsystem as a backend.

config MEASURED_BOOT_MAX_CBOR_SIZE
	int "Maximum CBOR size of boot state/measurements"
	default 64
	range 0 256
	depends on MEASURED_BOOT
	help
	  The maximum size of the CBOR message which stores boot
	  state/measurements.

choice BOOT_FAULT_INJECTION_HARDENING_PROFILE
	prompt "Fault injection hardening profile"
	default BOOT_FIH_PROFILE_OFF

config BOOT_FIH_PROFILE_OFF
	bool "No hardening against hardware level fault injection"
	help
	  No hardening in SW against hardware level fault injection: power or
	  clock glitching, etc.

config BOOT_FIH_PROFILE_LOW
	bool "Moderate level hardening against hardware level fault injection"
	help
	  Moderate level hardening: Long global fail loop to avoid break out,
	  control flow integrity check to discover discrepancy in expected code
	  flow.

config BOOT_FIH_PROFILE_MEDIUM
	bool "Medium level hardening against hardware level fault injection"
	help
	  Medium level hardening: Long global fail loop to avoid break out,
	  control flow integrity check to discover discrepancy in expected code
	  flow, double variables to discover register or memory corruption.

config BOOT_FIH_PROFILE_HIGH
	bool "Maximum level hardening against hardware level fault injection"
	select MBEDTLS
	help
	  Maximum level hardening: Long global fail loop to avoid break out,
	  control flow integrity check to discover discrepancy in expected code
	  flow, double variables to discover register or memory corruption, random
	  delays to make code execution less predictable. Random delays requires an
	  entropy source.

endchoice

choice BOOT_USB_DFU
	prompt "USB DFU"
	default BOOT_USB_DFU_NO

config BOOT_USB_DFU_NO
	prompt "Disabled"

config BOOT_USB_DFU_WAIT
	bool "Wait for a prescribed duration to see if USB DFU is invoked"
	select USB_DEVICE_STACK
	select USB_DFU_CLASS
	select IMG_MANAGER
	select STREAM_FLASH
	select MULTITHREADING
	help
	  If y, MCUboot waits for a prescribed duration of time to allow
	  for USB DFU to be invoked. Please note DFU always updates the
	  slot1 image.

config BOOT_USB_DFU_GPIO
	bool "Use GPIO to detect whether to trigger DFU mode"
	select USB_DEVICE_STACK
	select USB_DFU_CLASS
	select IMG_MANAGER
	select STREAM_FLASH
	select MULTITHREADING
	help
	  If y, MCUboot uses GPIO to detect whether to invoke USB DFU.

endchoice

config BOOT_USB_DFU_WAIT_DELAY_MS
	int "USB DFU wait duration"
	depends on BOOT_USB_DFU_WAIT
	default 12000
	help
	  Milliseconds to wait for USB DFU to be invoked.

if BOOT_USB_DFU_GPIO

config BOOT_USB_DFU_DETECT_DELAY
	int "Serial detect pin detection delay time [ms]"
	default 0
	help
	  Used to prevent the bootloader from loading on button press.
	  Useful for powering on when using the same button as
	  the one used to place the device in bootloader mode.

endif # BOOT_USB_DFU_GPIO

config BOOT_USB_DFU_NO_APPLICATION
	bool "Stay in bootloader if no application"
	help
	  Allows for entering USB DFU recovery mode if there is no bootable
	  application that the bootloader can jump to.

config BOOT_USE_BENCH
        bool "Enable benchmark code"
        default n
        help
          If y, adds support for simple benchmarking that can record
          time intervals between two calls.  The time printed depends
          on the particular Zephyr target, and is generally ticks of a
          specific board-specific timer.

module = MCUBOOT
module-str = MCUBoot bootloader
source "subsys/logging/Kconfig.template.log_config"

config MCUBOOT_LOG_THREAD_STACK_SIZE
	int "Stack size for the MCUBoot log processing thread"
	depends on LOG && !LOG_IMMEDIATE
	default 2048 if COVERAGE_GCOV
	default 1024 if NO_OPTIMIZATIONS
	default 1024 if XTENSA
	default 4096 if (X86 && X86_64)
	default 4096 if ARM64
	default 768
	help
	  Set the internal stack size for MCUBoot log processing thread.

config MCUBOOT_INDICATION_LED
	bool "Turns on LED indication when device is in DFU"
	select GPIO
	help
	  Device device activates the LED while in bootloader mode.
	  mcuboot-led0 alias must be set in the device's .dts
	  definitions for this to work.

rsource "Kconfig.serial_recovery"

rsource "Kconfig.firmware_loader"

config BOOT_INTR_VEC_RELOC
	bool "Relocate the interrupt vector to the application"
	default n
	depends on SW_VECTOR_RELAY || CPU_CORTEX_M_HAS_VTOR
	help
	  Relocate the interrupt vector to the application before it is started.
	  Select this option if application requires vector relocation,
	  but it doesn't relocate vector in its reset handler.

config UPDATEABLE_IMAGE_NUMBER
	int "Number of updateable images"
	default 1
	range 1 1 if SINGLE_APPLICATION_SLOT
	help
	  Enables support of multi image update.

config BOOT_VERSION_CMP_USE_BUILD_NUMBER
	bool "Use build number while comparing image version"
	depends on (UPDATEABLE_IMAGE_NUMBER > 1) || BOOT_DIRECT_XIP || \
		   BOOT_RAM_LOAD || MCUBOOT_DOWNGRADE_PREVENTION
	help
	  By default, the image version comparison relies only on version major,
	  minor and revision. Enable this option to take into account the build
	  number as well.

choice BOOT_DOWNGRADE_PREVENTION_CHOICE
	prompt "Downgrade prevention"
	optional

config MCUBOOT_DOWNGRADE_PREVENTION
	bool "SW based downgrade prevention"
	depends on !BOOT_DIRECT_XIP
	help
	  Prevent downgrades by enforcing incrementing version numbers.
	  When this option is set, any upgrade must have greater major version
	  or greater minor version with equal major version. This mechanism
	  only protects against some attacks against version downgrades (for
	  example, a JTAG could be used to write an older version).

config MCUBOOT_DOWNGRADE_PREVENTION_SECURITY_COUNTER
	bool "Use image security counter instead of version number"
	depends on MCUBOOT_DOWNGRADE_PREVENTION
	depends on (BOOT_SWAP_USING_MOVE || BOOT_SWAP_USING_SCRATCH)
	help
       Security counter is used for version eligibility check instead of pure
       version.  When this option is set, any upgrade must have greater or
       equal security counter value.
       Because of the acceptance of equal values it allows for software
       downgrades to some extent.

config MCUBOOT_HW_DOWNGRADE_PREVENTION
	bool "HW based downgrade prevention"
	help
	  Prevent undesirable/malicious software downgrades. When this option is
	  set, any upgrade must have greater or equal security counter value.
	  Because of the acceptance of equal values it allows for software
	  downgrade to some extent.

endchoice

config BOOT_WATCHDOG_FEED
	bool "Feed the watchdog while doing swap"
	default y if WATCHDOG
	default y if SOC_FAMILY_NORDIC_NRF
	# for nRF nrfx based implementation is available
	imply NRFX_WDT if SOC_FAMILY_NORDIC_NRF
	imply NRFX_WDT0 if SOC_FAMILY_NORDIC_NRF
	imply NRFX_WDT1 if SOC_FAMILY_NORDIC_NRF
	imply NRFX_WDT30 if SOC_FAMILY_NORDIC_NRF
	imply NRFX_WDT31 if SOC_FAMILY_NORDIC_NRF
	help
	  Enables implementation of MCUBOOT_WATCHDOG_FEED() macro which is
	  used to feed watchdog while doing time consuming operations.

config BOOT_IMAGE_ACCESS_HOOKS
	bool "Enable hooks for overriding MCUboot's native routines"
	help
	  Allow to provide procedures for override or extend native
	  MCUboot's routines required for access the image data and the image
	  update. It is up to the project customization to add required source
	  files to the build.

config MCUBOOT_ACTION_HOOKS
	bool "Enable hooks for responding to MCUboot status changes"
	help
	  This will call a handler when the MCUboot status changes which allows
	  for some level of user feedback, for instance to change LED status to
	  indicate a failure, using the callback:
	  'void mcuboot_status_change(mcuboot_status_type_t status)' where
	  'mcuboot_status_type_t' is listed in
	  boot/bootutil/include/bootutil/mcuboot_status.h

config BOOT_DISABLE_CACHES
	bool "Disable I/D caches before chain-loading application"
	depends on CPU_HAS_ICACHE || CPU_HAS_DCACHE
	default y
	help
	  Will flush and disable the instruction and data caches on the CPU prior to
	  booting an application, this is required on some ARM Cortex devices and
	  increases protection against data leakage from MCUboot to applications via
	  these caches.

config MCUBOOT_BOOT_BANNER
	bool "Use MCUboot boot banner"
	depends on BOOT_BANNER
	depends on !NCS_BOOT_BANNER
	depends on "$(APP_VERSION_EXTENDED_STRING)" != ""
	default y
	help
	  Uses a MCUboot boot banner instead of the default zephyr one, which will output the
	  MCUboot name and version, followed by the zephyr name and version.

	  For example:

	    *** Booting MCUboot v2.0.0-72-g8c0e36c88663 ***
	    *** Using Zephyr OS build v3.6.0-2607-gd0be2010c31f ***

config BOOT_BANNER_STRING
        default "Using Zephyr OS build" if MCUBOOT_BOOT_BANNER

config BOOT_DECOMPRESSION_SUPPORT
	bool
	depends on NRF_COMPRESS && NRF_COMPRESS_DECOMPRESSION && (NRF_COMPRESS_LZMA_VERSION_LZMA1 || NRF_COMPRESS_LZMA_VERSION_LZMA2)
	depends on !SINGLE_APPLICATION_SLOT && !BOOT_ENCRYPT_IMAGE && BOOT_UPGRADE_ONLY
	depends on UPDATEABLE_IMAGE_NUMBER = 1
	default y
	help
	  Hidden symbol which should be selected if a system provided decompression support.

if BOOT_DECOMPRESSION_SUPPORT

menuconfig BOOT_DECOMPRESSION
	bool "Decompression [EXPERIMENTAL]"
	select NRF_COMPRESS_CLEANUP
	select PM_USE_CONFIG_SRAM_SIZE if SOC_NRF54L15_CPUAPP
	select EXPERIMENTAL
	help
	  If enabled, will include support for compressed images being loaded to the secondary slot
	  which then get decompressed into the primary slot. This mode allows the secondary slot to
	  be smaller than primary slot which otherwise would not be allowed.

if BOOT_DECOMPRESSION

config BOOT_DECOMPRESSION_BUFFER_SIZE
	int
	range 16 16384
	default NRF_COMPRESS_CHUNK_SIZE
	help
	  The size of a secondary buffer used for writing decompressed data to the storage device.

endif # BOOT_DECOMPRESSION

endif # BOOT_DECOMPRESSION_SUPPORT

endmenu

config MCUBOOT_DEVICE_SETTINGS
	# Hidden selector for device-specific settings
	bool
	default y
        # CPU options
	select MCUBOOT_DEVICE_CPU_CORTEX_M0 if CPU_CORTEX_M0
        # Enable flash page layout if available
	select FLASH_PAGE_LAYOUT if FLASH_HAS_PAGE_LAYOUT
	# Enable flash_map module as flash I/O back-end
	select FLASH_MAP

config MCUBOOT_DEVICE_CPU_CORTEX_M0
	# Hidden selector for Cortex-M0 settings
	bool
	default n
	select SW_VECTOR_RELAY if !CPU_CORTEX_M0_HAS_VECTOR_TABLE_REMAP

comment "Zephyr configuration options"

# Disabling MULTITHREADING provides a code size advantage, but
# it requires peripheral drivers (particularly a flash driver)
# that works properly with the option enabled.
#
# If you know for sure that your hardware will work, you can default
# it to n here. Otherwise, having it on by default makes the most
# hardware work.
config MULTITHREADING
	default y if BOOT_SERIAL_CDC_ACM #usb driver requires MULTITHREADING
	default y if BOOT_USB_DFU_GPIO || BOOT_USB_DFU_WAIT
	default n if SOC_FAMILY_NORDIC_NRF
	default n if SOC_FAMILY_ESPRESSIF_ESP32 && MCUBOOT
	default y

config LOG_PROCESS_THREAD
	default n # mcuboot has its own log processing thread

# override USB device name
config USB_DEVICE_PRODUCT
	default "MCUBOOT"

# use MCUboot's own log configuration
config MCUBOOT_BOOTUTIL_LIB_OWN_LOG
	bool
	default n

config MCUBOOT_VERIFY_IMG_ADDRESS
	bool "Verify reset address of image in secondary slot"
	depends on UPDATEABLE_IMAGE_NUMBER > 1
	depends on !BOOT_ENCRYPT_IMAGE
	depends on ARM
	default y if BOOT_UPGRADE_ONLY
	help
	  Verify that the reset address in the image located in the secondary slot
	  is contained within the corresponding primary slot. This is recommended
	  if swapping is not used (that is, BOOT_UPGRADE_ONLY is set). If a user
	  incorrectly uploads an update for image 1 to image 0's secondary slot
	  MCUboot will overwrite image 0's primary slot with this image even
	  though it will not boot. If swapping is enabled this will be handled
	  since the image will not confirm itself. If, however, swapping is not
	  enabled then the only mitigation is serial recovery. This feature can
	  also be useful when BOOT_DIRECT_XIP is enabled, to ensure that the image
	  linked at the correct address is loaded.

source "Kconfig.zephyr"
