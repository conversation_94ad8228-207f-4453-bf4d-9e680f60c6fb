<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>/cygdrive/d/dev/code/coremark/core_list_join.c - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CFile"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="core_list_join.c"></a>core_list_join.c</h1><div class=CBody><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#core_list_join.c" >core_list_join.c</a></td><td class=SDescription></td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Description" >Description</a></td><td class=SDescription>Benchmark using a linked list.</td></tr><tr class="SGroup"><td class=SEntry><a href="#Functions" >Functions</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#cmp_complex" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">cmp_complex</a></td><td class=SDescription>Compare the data item in a list cell.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#cmp_idx" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')">cmp_idx</a></td><td class=SDescription>Compare the idx item in a list cell, and regen the data.</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#core_list_init" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')">core_list_init</a></td><td class=SDescription>Initialize list with data.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#core_list_insert" id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')">core_list_insert</a></td><td class=SDescription>Insert an item to the list</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#core_list_remove" id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')">core_list_remove</a></td><td class=SDescription>Remove an item from the list.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#core_list_undo_remove" id=link6 onMouseOver="ShowTip(event, 'tt6', 'link6')" onMouseOut="HideTip('tt6')">core_list_undo_remove</a></td><td class=SDescription>Undo a remove operation.</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#core_list_find" id=link7 onMouseOver="ShowTip(event, 'tt7', 'link7')" onMouseOut="HideTip('tt7')">core_list_find</a></td><td class=SDescription>Find an item in the list</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#core_list_reverse" id=link8 onMouseOver="ShowTip(event, 'tt8', 'link8')" onMouseOut="HideTip('tt8')">core_list_reverse</a></td><td class=SDescription>Reverse a list</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#core_list_mergesort" id=link9 onMouseOver="ShowTip(event, 'tt9', 'link9')" onMouseOut="HideTip('tt9')">core_list_mergesort</a></td><td class=SDescription>Sort the list in place without recursion.</td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Description"></a>Description</h3><div class=CBody><p>Benchmark using a linked list.</p><p>Linked list is a common data structure used in many applications.</p><p>For our purposes, this will excercise the memory units of the processor.&nbsp; In particular, usage of the list pointers to find and alter data.</p><p>We are not using Malloc since some platforms do not support this library.</p><p>Instead, the memory block being passed in is used to create a list, and the benchmark takes care not to add more items then can be accomodated by the memory block.&nbsp; The porting layer will make sure that we have a valid memory block.</p><p>All operations are done in place, without using any extra memory.</p><p>The list itself contains list pointers and pointers to data items.&nbsp; Data items contain the following:</p><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>idx</td><td class=CDLDescription>An index that captures the initial order of the list.</td></tr><tr><td class=CDLEntry>data</td><td class=CDLDescription>Variable data initialized based on the input parameters.&nbsp; The 16b are divided as follows:</td></tr></table><ul><li>Upper 8b are backup of original data.</li><li>Bit 7 indicates if the lower 7 bits are to be used as is or calculated.</li><li>Bits 0-2 indicate type of operation to perform to get a 7b value.</li><li>Bits 3-6 provide input for the operation.</li></ul></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="Functions"></a>Functions</h3></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="cmp_complex"></a>cmp_complex</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s32 cmp_complex(</td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>a,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>b,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Compare the data item in a list cell.</p><p>Can be used by mergesort.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="cmp_idx"></a>cmp_idx</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s32 cmp_idx(</td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>a,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>b,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Compare the idx item in a list cell, and regen the data.</p><p>Can be used by mergesort.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_list_init"></a>core_list_init</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_init(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>blksize,</td></tr><tr><td></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Initialize list with data.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>blksize</td><td class=CDLDescription>Size of memory to be initialized.</td></tr><tr><td class=CDLEntry>memblock</td><td class=CDLDescription>Pointer to memory block.</td></tr><tr><td class=CDLEntry>seed</td><td class=CDLDescription>Actual values chosen depend on the seed parameter.&nbsp; The seed parameter MUST be supplied from a source that cannot be determined at compile time</td></tr></table><h4 class=CHeading>Returns</h4><p>Pointer to the head of the list.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_list_insert"></a>core_list_insert</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_insert_new(</td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>insert_point,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>info,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>**</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PTypePrefix nowrap>list_data&nbsp;</td><td class=PType nowrap>**datablock&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock_end,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>datablock_end</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Insert an item to the list</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>insert_point</td><td class=CDLDescription>where to insert the item.</td></tr><tr><td class=CDLEntry>info</td><td class=CDLDescription>data for the cell.</td></tr><tr><td class=CDLEntry>memblock</td><td class=CDLDescription>pointer for the list header</td></tr><tr><td class=CDLEntry>datablock</td><td class=CDLDescription>pointer for the list data</td></tr><tr><td class=CDLEntry>memblock_end</td><td class=CDLDescription>end of region for list headers</td></tr><tr><td class=CDLEntry>datablock_end</td><td class=CDLDescription>end of region for list data</td></tr></table><h4 class=CHeading>Returns</h4><p>Pointer to new item.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_list_remove"></a>core_list_remove</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_remove(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Remove an item from the list.</p><h4 class=CHeading>Operation</h4><p>For a singly linked list, remove by copying the data from the next item over to the current cell, and unlinking the next item.</p><h4 class=CHeading>Note</h4><p>since there is always a fake item at the end of the list, no need to check for NULL.</p><h4 class=CHeading>Returns</h4><p>Removed item.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_list_undo_remove"></a>core_list_undo_remove</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_undo_remove(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item_removed,</td></tr><tr><td></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item_modified</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Undo a remove operation.</p><h4 class=CHeading>Operation</h4><p>Since we want each iteration of the benchmark to be exactly the same, we need to be able to undo a remove.&nbsp; Link the removed item back into the list, and switch the info items.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>item_removed</td><td class=CDLDescription>Return value from the <a href="#core_list_remove" class=LFunction id=link10 onMouseOver="ShowTip(event, 'tt5', 'link10')" onMouseOut="HideTip('tt5')">core_list_remove</a></td></tr><tr><td class=CDLEntry>item_modified</td><td class=CDLDescription>List item that was modified during <a href="#core_list_remove" class=LFunction id=link11 onMouseOver="ShowTip(event, 'tt5', 'link11')" onMouseOut="HideTip('tt5')">core_list_remove</a></td></tr></table><h4 class=CHeading>Returns</h4><p>The item that was linked back to the list.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_list_find"></a>core_list_find</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_find(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>info</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Find an item in the list</p><h4 class=CHeading>Operation</h4><p>Find an item by idx (if not 0) or specific data value</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>list</td><td class=CDLDescription>list head</td></tr><tr><td class=CDLEntry>info</td><td class=CDLDescription>idx or data to find</td></tr></table><h4 class=CHeading>Returns</h4><p>Found item, or NULL if not found.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_list_reverse"></a>core_list_reverse</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_reverse(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Reverse a list</p><h4 class=CHeading>Operation</h4><p>Rearrange the pointers so the list is reversed.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>list</td><td class=CDLDescription>list head</td></tr><tr><td class=CDLEntry>info</td><td class=CDLDescription>idx or data to find</td></tr></table><h4 class=CHeading>Returns</h4><p>Found item, or NULL if not found.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_list_mergesort"></a>core_list_mergesort</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_mergesort(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list,</td></tr><tr><td></td><td class=PType nowrap>list_cmp&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>cmp,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Sort the list in place without recursion.</p><h4 class=CHeading>Description</h4><p>Use mergesort, as for linked list this is a realistic solution.&nbsp; Also, since this is aimed at embedded, care was taken to use iterative rather then recursive algorithm.&nbsp; The sort can either return the list to original order (by idx) , or use the data item to invoke other other algorithms and change the order of the list.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>list</td><td class=CDLDescription>list to be sorted.</td></tr><tr><td class=CDLEntry>cmp</td><td class=CDLDescription>cmp function to use</td></tr></table><h4 class=CHeading>Returns</h4><p>New head of the list.</p><h4 class=CHeading>Note</h4><p>We have a special header for the list that will always be first, but the algorithm could theoretically modify where the list starts.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile id=MSelected>core_list_join.c</div></div><div class=MEntry><div class=MFile><a href="core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="../index/BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s32 cmp_complex(</td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>a,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>b,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Compare the data item in a list cell.</div></div><div class=CToolTip id="tt2"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s32 cmp_idx(</td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>a,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>b,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Compare the idx item in a list cell, and regen the data.</div></div><div class=CToolTip id="tt3"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_init(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>blksize,</td></tr><tr><td></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Initialize list with data.</div></div><div class=CToolTip id="tt4"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_insert_new(</td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>insert_point,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>info,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>**</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PTypePrefix nowrap>list_data&nbsp;</td><td class=PType nowrap>**datablock&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock_end,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>datablock_end</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Insert an item to the list</div></div><div class=CToolTip id="tt5"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_remove(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Remove an item from the list.</div></div><div class=CToolTip id="tt6"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_undo_remove(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item_removed,</td></tr><tr><td></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item_modified</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Undo a remove operation.</div></div><div class=CToolTip id="tt7"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_find(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>info</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Find an item in the list</div></div><div class=CToolTip id="tt8"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_reverse(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Reverse a list</div></div><div class=CToolTip id="tt9"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_mergesort(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list,</td></tr><tr><td></td><td class=PType nowrap>list_cmp&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>cmp,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Sort the list in place without recursion.</div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>