# The UART is used for Serial Recovery, so logging requires
# an RTT console, which is not available out of the box on this board.
# Disable logging.
CONFIG_LOG=n

# Serial
CONFIG_CONSOLE=n
CONFIG_SERIAL=y
CONFIG_UART_NRFX=n
CONFIG_UART_INTERRUPT_DRIVEN=y
CONFIG_UART_LINE_CTRL=y

# MCUBoot serial
CONFIG_GPIO=y
CONFIG_MCUBOOT_SERIAL=y
CONFIG_BOOT_SERIAL_CDC_ACM=y

# Required by USB
CONFIG_MULTITHREADING=y

# USB
CONFIG_USB_DEVICE_STACK=y
CONFIG_USB_DEVICE_REMOTE_WAKEUP=n
CONFIG_USB_DEVICE_PRODUCT="MCUBOOT"

CONFIG_NORDIC_QSPI_NOR=n
