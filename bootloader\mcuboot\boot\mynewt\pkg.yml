#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

pkg.name: boot/mynewt
pkg.type: app
pkg.description: "Mynewt port of mcuboot"
pkg.author: "Apache Mynewt <<EMAIL>>"
pkg.homepage: "http://mynewt.apache.org/"
pkg.keywords:
    - loader

pkg.cflags:
    - "-DMCUBOOT_MYNEWT=1"

pkg.deps:
    - "@mcuboot/boot/mynewt/mcuboot_config"
    - "@mcuboot/boot/bootutil"
    - "@mcuboot/boot/mynewt/flash_map_backend"
    - "@apache-mynewt-core/kernel/os"
    - "@apache-mynewt-core/sys/log/stub"

pkg.ign_files.!BOOTUTIL_SINGLE_APPLICATION_SLOT:
    - "single_loader.c"
pkg.ign_files.BOOTUTIL_SINGLE_APPLICATION_SLOT:
    - "swap_scratch.c"

pkg.deps.BOOTUTIL_NO_LOGGING:
    - "@apache-mynewt-core/sys/console/stub"

pkg.deps.BOOTUTIL_HAVE_LOGGING:
    - "@apache-mynewt-core/sys/console/minimal"

pkg.deps.BOOT_SERIAL:
    - "@mcuboot/boot/boot_serial"
    - "@mcuboot/boot/mynewt/boot_uart"
