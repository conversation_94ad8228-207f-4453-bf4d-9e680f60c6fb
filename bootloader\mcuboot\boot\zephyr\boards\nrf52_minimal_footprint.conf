# Minimal MCUBoot flash footprint configuration
# for nRF52832 SoC targets
# This is not recomendet configuration because of security and reliability
# reasons.


# Generated by <PERSON><PERSON><PERSON>glib (https://github.com/ulfalizer/Kconfiglib)
CONFIG_BOOT_SIGNATURE_TYPE_ECDSA_P256=y
CONFIG_BOOT_SIGNATURE_KEY_FILE="root-ec-p256.pem"

# In any real project CONFIG_BOOT_VALIDATE_SLOT0 enabling is recommended
# by security reason.
# CONFIG_BOOT_VALIDATE_SLOT0 is not set

# In most of projects CONFIG_BOOT_UPGRADE_ONLY disabling is recommended
# by reliability reason.
CONFIG_BOOT_UPGRADE_ONLY=y

# CONFIG_BOARD_ENABLE_DCDC is not set
CONFIG_SOC_SERIES_NRF52X=y
CONFIG_SOC_NRF52832_QFAA=y
CONFIG_ARM=y
CONFIG_ARM_MPU=n
CONFIG_MAIN_STACK_SIZE=10240
CONFIG_THREAD_STACK_INFO=n
# CONFIG_TICKLESS_KERNEL is not set
CONFIG_FLASH=y

CONFIG_CONSOLE=n
CONFIG_DEBUG=n
CONFIG_EARLY_CONSOLE=n
CONFIG_PRINTK=n

CONFIG_SYS_CLOCK_EXISTS=n

# Drivers and peripherals
CONFIG_I2C=n
CONFIG_WATCHDOG=n
CONFIG_GPIO=n
CONFIG_PINMUX=n
CONFIG_SPI=n
CONFIG_SERIAL=n

# Power management
CONFIG_PM=n

# Interrupts
CONFIG_DYNAMIC_INTERRUPTS=n
CONFIG_IRQ_OFFLOAD=n

# Memory protection
CONFIG_MEMORY_PROTECTION=n
CONFIG_THREAD_CUSTOM_DATA=n
CONFIG_FPU=n

# Boot
CONFIG_BOOT_BANNER=n
CONFIG_BOOT_DELAY=0

# Console
CONFIG_STDOUT_CONSOLE=n
