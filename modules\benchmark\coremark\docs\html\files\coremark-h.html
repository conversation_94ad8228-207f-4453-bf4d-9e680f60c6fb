<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>/cygdrive/d/dev/code/coremark/coremark.h - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CFile"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="coremark.h"></a>coremark.h</h1><div class=CBody><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#coremark.h" >coremark.h</a></td><td class=SDescription></td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Description" >Description</a></td><td class=SDescription>This file contains  declarations of the various benchmark functions.</td></tr><tr class="SGroup"><td class=SEntry><a href="#Configuration" >Configuration</a></td><td class=SDescription></td></tr><tr class="SConfiguration SIndent1 SMarked"><td class=SEntry><a href="#TOTAL_DATA_SIZE" >TOTAL_DATA_SIZE</a></td><td class=SDescription>Define total size for data algorithms will operate on</td></tr><tr class="SGroup"><td class=SEntry><a href="#Types" >Types</a></td><td class=SDescription></td></tr><tr class="SType SIndent1 SMarked"><td class=SEntry><a href="#secs_ret" >secs_ret</a></td><td class=SDescription>For machines that have floating point support, get number of seconds as a double. </td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Description"></a>Description</h3><div class=CBody><p>This file contains  declarations of the various benchmark functions.</p></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="Configuration"></a>Configuration</h3></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="TOTAL_DATA_SIZE"></a>TOTAL_DATA_SIZE</h3><div class=CBody><p>Define total size for data algorithms will operate on</p></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="Types"></a>Types</h3></div></div>

<div class="CType"><div class=CTopic><h3 class=CTitle><a name="secs_ret"></a>secs_ret</h3><div class=CBody><p>For machines that have floating point support, get number of seconds as a double.&nbsp; Otherwise an unsigned int.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile id=MSelected>coremark.h</div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="../index/BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>