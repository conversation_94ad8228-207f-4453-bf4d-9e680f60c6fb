<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>Index - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="IndexPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=IPageTitle>Index</div><div class=INavigationBar>$#! &middot; 0-9 &middot; A &middot; <a href="#B">B</a> &middot; <a href="#C">C</a> &middot; <a href="#D">D</a> &middot; E &middot; <a href="#F">F</a> &middot; <a href="#G">G</a> &middot; <a href="#H">H</a> &middot; <a href="#I">I</a> &middot; J &middot; K &middot; <a href="#L">L</a> &middot; <a href="#M">M</a> &middot; N &middot; <a href="#O">O</a> &middot; <a href="#P">P</a> &middot; Q &middot; <a href="#R">R</a> &middot; <a href="General2.html#S">S</a> &middot; <a href="General2.html#T">T</a> &middot; <a href="General2.html#U">U</a> &middot; <a href="General2.html#V">V</a> &middot; <a href="General2.html#W">W</a> &middot; X &middot; Y &middot; Z</div><table border=0 cellspacing=0 cellpadding=0><tr><td class=IHeading id=IFirstHeading><a name="B"></a>B</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>Build Targets</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#Build_Targets"  class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#Build_Targets"  class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#Building_and_running" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')" class=ISymbol>Building and running</a></td></tr><tr><td class=IHeading><a name="C"></a>C</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-mak.html#CC" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')" class=ISymbol>CC</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>CFLAGS</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#CFLAGS" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#CFLAGS" id=link4 onMouseOver="ShowTip(event, 'tt3', 'link4')" onMouseOut="HideTip('tt3')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#cmp_complex" id=link5 onMouseOver="ShowTip(event, 'tt4', 'link5')" onMouseOut="HideTip('tt4')" class=ISymbol>cmp_complex</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#cmp_idx" id=link6 onMouseOver="ShowTip(event, 'tt5', 'link6')" onMouseOut="HideTip('tt5')" class=ISymbol>cmp_idx</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>Configuration</span><div class=ISubIndex><a href="../files/coremark-h.html#Configuration"  class=IFile>coremark.h</a><a href="../files/linux/core_portme-h.html#Configuration"  class=IFile>linux/<span class=HB> </span>core_portme.h</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#core_bench_matrix" id=link7 onMouseOver="ShowTip(event, 'tt6', 'link7')" onMouseOut="HideTip('tt6')" class=ISymbol>core_bench_matrix</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_state-c.html#core_bench_state" id=link8 onMouseOver="ShowTip(event, 'tt7', 'link8')" onMouseOut="HideTip('tt7')" class=ISymbol>core_bench_state</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_state-c.html#core_init_state" id=link9 onMouseOver="ShowTip(event, 'tt8', 'link9')" onMouseOut="HideTip('tt8')" class=ISymbol>core_init_state</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_find" id=link10 onMouseOver="ShowTip(event, 'tt9', 'link10')" onMouseOut="HideTip('tt9')" class=ISymbol>core_list_find</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_init" id=link11 onMouseOver="ShowTip(event, 'tt10', 'link11')" onMouseOut="HideTip('tt10')" class=ISymbol>core_list_init</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_insert" id=link12 onMouseOver="ShowTip(event, 'tt11', 'link12')" onMouseOut="HideTip('tt11')" class=ISymbol>core_list_insert</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_join.c"  class=ISymbol>core_list_join.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_mergesort" id=link13 onMouseOver="ShowTip(event, 'tt12', 'link13')" onMouseOut="HideTip('tt12')" class=ISymbol>core_list_mergesort</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_remove" id=link14 onMouseOver="ShowTip(event, 'tt13', 'link14')" onMouseOut="HideTip('tt13')" class=ISymbol>core_list_remove</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_reverse" id=link15 onMouseOver="ShowTip(event, 'tt14', 'link15')" onMouseOut="HideTip('tt14')" class=ISymbol>core_list_reverse</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_undo_remove" id=link16 onMouseOver="ShowTip(event, 'tt15', 'link16')" onMouseOut="HideTip('tt15')" class=ISymbol>core_list_undo_remove</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_main-c.html#core_main.c" id=link17 onMouseOver="ShowTip(event, 'tt16', 'link17')" onMouseOut="HideTip('tt16')" class=ISymbol>core_main.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#core_matrix.c"  class=ISymbol>core_matrix.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#core_portme.c"  class=ISymbol>core_portme.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#core_portme.h"  class=ISymbol>core_portme.h</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>core_portme.mak</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#core_portme.mak"  class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#core_portme.mak"  class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#core_start_parallel" id=link18 onMouseOver="ShowTip(event, 'tt17', 'link18')" onMouseOut="HideTip('tt17')" class=ISymbol>core_start_parallel</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_state-c.html#core_state.c"  class=ISymbol>core_state.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_state-c.html#core_state_transition" id=link19 onMouseOver="ShowTip(event, 'tt18', 'link19')" onMouseOut="HideTip('tt18')" class=ISymbol>core_state_transition</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#core_stop_parallel" id=link20 onMouseOver="ShowTip(event, 'tt19', 'link20')" onMouseOut="HideTip('tt19')" class=ISymbol>core_stop_parallel</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#CORE_TICKS" id=link21 onMouseOver="ShowTip(event, 'tt20', 'link21')" onMouseOut="HideTip('tt20')" class=ISymbol>CORE_TICKS</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_util-c.html#core_util.c"  class=ISymbol>core_util.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#CoreMark"  class=ISymbol>CoreMark</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#coremark.h"  class=ISymbol>coremark.h</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_util-c.html#crc*" id=link22 onMouseOver="ShowTip(event, 'tt21', 'link22')" onMouseOut="HideTip('tt21')" class=ISymbol>crc*</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#Credits" id=link23 onMouseOver="ShowTip(event, 'tt22', 'link23')" onMouseOut="HideTip('tt22')" class=ISymbol>Credits</a></td></tr><tr><td class=IHeading><a name="D"></a>D</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#default_num_contexts" id=link24 onMouseOver="ShowTip(event, 'tt23', 'link24')" onMouseOut="HideTip('tt23')" class=ISymbol>default_num_contexts</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>Description</span><div class=ISubIndex><a href="../files/core_list_join-c.html#Description" id=link25 onMouseOver="ShowTip(event, 'tt24', 'link25')" onMouseOut="HideTip('tt24')" class=IFile>core_list_join.c</a><a href="../files/core_matrix-c.html#Description" id=link26 onMouseOver="ShowTip(event, 'tt24', 'link26')" onMouseOut="HideTip('tt24')" class=IFile>core_matrix.c</a><a href="../files/core_state-c.html#Description" id=link27 onMouseOver="ShowTip(event, 'tt24', 'link27')" onMouseOut="HideTip('tt24')" class=IFile>core_state.c</a><a href="../files/coremark-h.html#Description" id=link28 onMouseOver="ShowTip(event, 'tt24', 'link28')" onMouseOut="HideTip('tt24')" class=IFile>coremark.h</a><a href="../files/linux/core_portme-h.html#Description" id=link29 onMouseOver="ShowTip(event, 'tt24', 'link29')" onMouseOut="HideTip('tt24')" class=IFile>linux/<span class=HB> </span>core_portme.h</a></div></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#Documentation" id=link30 onMouseOver="ShowTip(event, 'tt25', 'link30')" onMouseOut="HideTip('tt25')" class=ISymbol>Documentation</a></td></tr><tr><td class=IHeading><a name="F"></a>F</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>Functions</span><div class=ISubIndex><a href="../files/core_list_join-c.html#Functions"  class=IFile>core_list_join.c</a><a href="../files/core_main-c.html#Functions"  class=IFile>core_main.c</a><a href="../files/core_matrix-c.html#Functions"  class=IFile>core_matrix.c</a><a href="../files/core_state-c.html#Functions"  class=IFile>core_state.c</a><a href="../files/core_util-c.html#Functions"  class=IFile>core_util.c</a></div></td></tr><tr><td class=IHeading><a name="G"></a>G</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_util-c.html#get_seed" id=link31 onMouseOver="ShowTip(event, 'tt26', 'link31')" onMouseOut="HideTip('tt26')" class=ISymbol>get_seed</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#get_time" id=link32 onMouseOver="ShowTip(event, 'tt27', 'link32')" onMouseOut="HideTip('tt27')" class=ISymbol>get_time</a></td></tr><tr><td class=IHeading><a name="H"></a>H</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#HAS_FLOAT" id=link33 onMouseOver="ShowTip(event, 'tt28', 'link33')" onMouseOut="HideTip('tt28')" class=ISymbol>HAS_FLOAT</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#HAS_PRINTF" id=link34 onMouseOver="ShowTip(event, 'tt29', 'link34')" onMouseOut="HideTip('tt29')" class=ISymbol>HAS_PRINTF</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#HAS_STDIO" id=link35 onMouseOver="ShowTip(event, 'tt30', 'link35')" onMouseOut="HideTip('tt30')" class=ISymbol>HAS_STDIO</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#HAS_TIME_H" id=link36 onMouseOver="ShowTip(event, 'tt31', 'link36')" onMouseOut="HideTip('tt31')" class=ISymbol>HAS_TIME_H</a></td></tr><tr><td class=IHeading><a name="I"></a>I</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_main-c.html#iterate" id=link37 onMouseOver="ShowTip(event, 'tt32', 'link37')" onMouseOut="HideTip('tt32')" class=ISymbol>iterate</a></td></tr><tr><td class=IHeading><a name="L"></a>L</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#Legal" id=link38 onMouseOver="ShowTip(event, 'tt33', 'link38')" onMouseOut="HideTip('tt33')" class=ISymbol>Legal</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>LFLAGS_END</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#LFLAGS_END" id=link39 onMouseOver="ShowTip(event, 'tt34', 'link39')" onMouseOut="HideTip('tt34')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#LFLAGS_END" id=link40 onMouseOver="ShowTip(event, 'tt34', 'link40')" onMouseOut="HideTip('tt34')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-mak.html#LOAD" id=link41 onMouseOver="ShowTip(event, 'tt35', 'link41')" onMouseOut="HideTip('tt35')" class=ISymbol>LOAD</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#Log_File_Format" id=link42 onMouseOver="ShowTip(event, 'tt36', 'link42')" onMouseOut="HideTip('tt36')" class=ISymbol>Log File Format</a></td></tr><tr><td class=IHeading><a name="M"></a>M</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_main-c.html#main" id=link43 onMouseOver="ShowTip(event, 'tt37', 'link43')" onMouseOut="HideTip('tt37')" class=ISymbol>main</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#MAIN_HAS_NOARGC" id=link44 onMouseOver="ShowTip(event, 'tt38', 'link44')" onMouseOut="HideTip('tt38')" class=ISymbol>MAIN_HAS_NOARGC</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#MAIN_HAS_NORETURN" id=link45 onMouseOver="ShowTip(event, 'tt39', 'link45')" onMouseOut="HideTip('tt39')" class=ISymbol>MAIN_HAS_NORETURN</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_add_const" id=link46 onMouseOver="ShowTip(event, 'tt40', 'link46')" onMouseOut="HideTip('tt40')" class=ISymbol>matrix_add_const</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_mul_const" id=link47 onMouseOver="ShowTip(event, 'tt41', 'link47')" onMouseOut="HideTip('tt41')" class=ISymbol>matrix_mul_const</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_mul_matrix" id=link48 onMouseOver="ShowTip(event, 'tt42', 'link48')" onMouseOut="HideTip('tt42')" class=ISymbol>matrix_mul_matrix</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_mul_matrix_bitextract" id=link49 onMouseOver="ShowTip(event, 'tt43', 'link49')" onMouseOut="HideTip('tt43')" class=ISymbol>matrix_mul_matrix_bitextract</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_mul_vect" id=link50 onMouseOver="ShowTip(event, 'tt44', 'link50')" onMouseOut="HideTip('tt44')" class=ISymbol>matrix_mul_vect</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_sum" id=link51 onMouseOver="ShowTip(event, 'tt45', 'link51')" onMouseOut="HideTip('tt45')" class=ISymbol>matrix_sum</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_test" id=link52 onMouseOver="ShowTip(event, 'tt46', 'link52')" onMouseOut="HideTip('tt46')" class=ISymbol>matrix_test</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#MEM_METHOD" id=link53 onMouseOver="ShowTip(event, 'tt47', 'link53')" onMouseOut="HideTip('tt47')" class=ISymbol>MEM_METHOD</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#MULTITHREAD" id=link54 onMouseOver="ShowTip(event, 'tt48', 'link54')" onMouseOut="HideTip('tt48')" class=ISymbol>MULTITHREAD</a></td></tr><tr><td class=IHeading><a name="O"></a>O</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>OPATH</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#OPATH"  class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#OPATH"  class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>OUTFLAG</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#OUTFLAG" id=link55 onMouseOver="ShowTip(event, 'tt49', 'link55')" onMouseOut="HideTip('tt49')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#OUTFLAG" id=link56 onMouseOver="ShowTip(event, 'tt49', 'link56')" onMouseOut="HideTip('tt49')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=IHeading><a name="P"></a>P</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>PERL</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#PERL" id=link57 onMouseOver="ShowTip(event, 'tt50', 'link57')" onMouseOut="HideTip('tt50')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#PERL" id=link58 onMouseOver="ShowTip(event, 'tt50', 'link58')" onMouseOut="HideTip('tt50')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>PORT_OBJS</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#PORT_OBJS" id=link59 onMouseOver="ShowTip(event, 'tt51', 'link59')" onMouseOut="HideTip('tt51')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#PORT_OBJS" id=link60 onMouseOver="ShowTip(event, 'tt51', 'link60')" onMouseOut="HideTip('tt51')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_postbuild</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postbuild" id=link61 onMouseOver="ShowTip(event, 'tt52', 'link61')" onMouseOut="HideTip('tt52')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postbuild" id=link62 onMouseOver="ShowTip(event, 'tt52', 'link62')" onMouseOut="HideTip('tt52')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_postload</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postload" id=link63 onMouseOver="ShowTip(event, 'tt53', 'link63')" onMouseOut="HideTip('tt53')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postload" id=link64 onMouseOver="ShowTip(event, 'tt53', 'link64')" onMouseOut="HideTip('tt53')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_postrun</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postrun" id=link65 onMouseOver="ShowTip(event, 'tt54', 'link65')" onMouseOut="HideTip('tt54')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postrun" id=link66 onMouseOver="ShowTip(event, 'tt54', 'link66')" onMouseOut="HideTip('tt54')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_prebuild</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_prebuild" id=link67 onMouseOver="ShowTip(event, 'tt55', 'link67')" onMouseOut="HideTip('tt55')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_prebuild" id=link68 onMouseOver="ShowTip(event, 'tt55', 'link68')" onMouseOut="HideTip('tt55')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_preload</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_preload" id=link69 onMouseOver="ShowTip(event, 'tt56', 'link69')" onMouseOut="HideTip('tt56')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_preload" id=link70 onMouseOver="ShowTip(event, 'tt56', 'link70')" onMouseOut="HideTip('tt56')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>port_prerun</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_prerun" id=link71 onMouseOver="ShowTip(event, 'tt57', 'link71')" onMouseOut="HideTip('tt57')" class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_prerun" id=link72 onMouseOver="ShowTip(event, 'tt57', 'link72')" onMouseOut="HideTip('tt57')" class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-mak.html#PORT_SRCS" id=link73 onMouseOver="ShowTip(event, 'tt58', 'link73')" onMouseOut="HideTip('tt58')" class=ISymbol>PORT_SRCS</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#portable_fini" id=link74 onMouseOver="ShowTip(event, 'tt59', 'link74')" onMouseOut="HideTip('tt59')" class=ISymbol>portable_fini</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#portable_free" id=link75 onMouseOver="ShowTip(event, 'tt60', 'link75')" onMouseOut="HideTip('tt60')" class=ISymbol>portable_free</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#portable_init" id=link76 onMouseOver="ShowTip(event, 'tt61', 'link76')" onMouseOut="HideTip('tt61')" class=ISymbol>portable_init</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#portable_malloc" id=link77 onMouseOver="ShowTip(event, 'tt62', 'link77')" onMouseOut="HideTip('tt62')" class=ISymbol>portable_malloc</a></td></tr><tr><td class=IHeading><a name="R"></a>R</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/release_notes-txt.html#Release_Notes" id=link78 onMouseOver="ShowTip(event, 'tt63', 'link78')" onMouseOut="HideTip('tt63')" class=ISymbol>Release Notes</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#Reporting_rules" id=link79 onMouseOver="ShowTip(event, 'tt64', 'link79')" onMouseOut="HideTip('tt64')" class=ISymbol>Reporting rules</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-mak.html#RUN" id=link80 onMouseOver="ShowTip(event, 'tt65', 'link80')" onMouseOut="HideTip('tt65')" class=ISymbol>RUN</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#Run_rules" id=link81 onMouseOver="ShowTip(event, 'tt66', 'link81')" onMouseOut="HideTip('tt66')" class=ISymbol>Run rules</a></td></tr></table>
<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CGeneric>Download the release files from the www.coremark.org. </div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt2"><div class=CVariable>Use this flag to define compiler to use</div></div><div class=CToolTip id="tt3"><div class=CVariable>Use this flag to define compiler options. </div></div><div class=CToolTip id="tt4"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s32 cmp_complex(</td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>a,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>b,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Compare the data item in a list cell.</div></div><div class=CToolTip id="tt5"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s32 cmp_idx(</td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>a,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>b,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Compare the idx item in a list cell, and regen the data.</div></div><div class=CToolTip id="tt6"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_u16 core_bench_matrix(</td><td class=PType nowrap>mat_params&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed,</td></tr><tr><td></td><td class=PType nowrap>ee_u16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>crc</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Benchmark function</div></div><div class=CToolTip id="tt7"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_u16 core_bench_state(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>blksize,</td></tr><tr><td></td><td class=PType nowrap>ee_u8&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed1,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed2,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>step,</td></tr><tr><td></td><td class=PType nowrap>ee_u16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>crc</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Benchmark function</div></div><div class=CToolTip id="tt8"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void core_init_state(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>size,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed,</td></tr><tr><td></td><td class=PType nowrap>ee_u8&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Initialize the input data for the state machine.</div></div><div class=CToolTip id="tt9"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_find(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>info</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Find an item in the list</div></div><div class=CToolTip id="tt10"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_init(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>blksize,</td></tr><tr><td></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Initialize list with data.</div></div><div class=CToolTip id="tt11"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_insert_new(</td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>insert_point,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>info,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>**</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PTypePrefix nowrap>list_data&nbsp;</td><td class=PType nowrap>**datablock&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock_end,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>datablock_end</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Insert an item to the list</div></div><div class=CToolTip id="tt12"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_mergesort(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list,</td></tr><tr><td></td><td class=PType nowrap>list_cmp&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>cmp,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Sort the list in place without recursion.</div></div><div class=CToolTip id="tt13"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_remove(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Remove an item from the list.</div></div><div class=CToolTip id="tt14"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_reverse(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Reverse a list</div></div><div class=CToolTip id="tt15"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_undo_remove(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item_removed,</td></tr><tr><td></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item_modified</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Undo a remove operation.</div></div><div class=CToolTip id="tt16"><div class=CFile>This file contains the framework to acquire a block of memory, seed initial parameters, tun t he benchmark and report the results.</div></div><div class=CToolTip id="tt17"><div class=CFunction>Start benchmarking in a parallel context.</div></div><div class=CToolTip id="tt18"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>enum CORE_STATE core_state_transition(</td><td class=PTypePrefix nowrap>ee_u8&nbsp;</td><td class=PType nowrap>**instr&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>transition_count</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Actual state machine.</div></div><div class=CToolTip id="tt19"><div class=CFunction>Stop a parallel context execution of coremark, and gather the results.</div></div><div class=CToolTip id="tt20"><div class=CConfiguration>Define type of return from the timing functions.</div></div><div class=CToolTip id="tt21"><div class=CFunction>Service functions to calculate 16b CRC code.</div></div><div class=CToolTip id="tt22"><div class=CGeneric>Many thanks to all of the individuals who helped with the development or testing of CoreMark including (Sorted by company name)</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt23"><div class=CVariable><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td>extern ee_u32 default_num_contexts</td></tr></table></blockquote>Number of contexts to spawn in multicore context. </div></div><div class=CToolTip id="tt24"><div class=CGeneric>Benchmark using a linked list.</div></div><div class=CToolTip id="tt25"><div class=CGeneric>When you unpack the documentation (tar -vzxf coremark_version_docs.tgz) a docs folder will be created. </div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt26"><div class=CFunction>Get a values that cannot be determined at compile time.</div></div><div class=CToolTip id="tt27"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>CORE_TICKS get_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Return an abstract &ldquo;ticks&rdquo; number that signifies time on the system.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt28"><div class=CConfiguration>Define to 1 if the platform supports floating point.</div></div><div class=CToolTip id="tt29"><div class=CConfiguration>Define to 1 if the platform has stdio.h and implements the printf function.</div></div><div class=CToolTip id="tt30"><div class=CConfiguration>Define to 1 if the platform has stdio.h.</div></div><div class=CToolTip id="tt31"><div class=CConfiguration>Define to 1 if platform has the time.h header file, and implementation of functions thereof.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt32"><div class=CFunction>Run the benchmark for a specified number of iterations.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt33"><div class=CGeneric>See LICENSE.txt or the word document file under docs/LICENSE.doc. </div></div><div class=CToolTip id="tt34"><div class=CVariable>Define any libraries needed for linking or other flags that should come at the end of the link line (e.g. </div></div><div class=CToolTip id="tt35"><div class=CVariable>Define this flag if you need to load to a target, as in a cross compile environment.</div></div><div class=CToolTip id="tt36"><div class=CGeneric>The log files have the following format</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt37"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>#if MAIN_HAS_NOARGC MAIN_RETURN_TYPE main(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Main entry routine for the benchmark. </div></div><div class=CToolTip id="tt38"><div class=CConfiguration>Needed if platform does not support getting arguments to main.</div></div><div class=CToolTip id="tt39"><div class=CConfiguration>Needed if platform does not support returning a value from main.</div></div><div class=CToolTip id="tt40"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_add_const(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Add a constant value to all elements of a matrix.</div></div><div class=CToolTip id="tt41"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_const(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a constant. </div></div><div class=CToolTip id="tt42"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_matrix(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a matrix. </div></div><div class=CToolTip id="tt43"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_matrix_bitextract(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a matrix, and extract some bits from the result. </div></div><div class=CToolTip id="tt44"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_vect(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a vector. </div></div><div class=CToolTip id="tt45"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s16 matrix_sum(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>clipval</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Calculate a function that depends on the values of elements in the matrix.</div></div><div class=CToolTip id="tt46"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s16 matrix_test(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Perform matrix manipulation.</div></div><div class=CToolTip id="tt47"><div class=CConfiguration>Defines method to get a block of memry.</div></div><div class=CToolTip id="tt48"><div class=CConfiguration>Define for parallel execution</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt49"><div class=CVariable>Use this flag to define how to to get an executable (e.g -o)</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt50"><div class=CVariable>Define perl executable to calculate the geomean if running separate.</div></div><div class=CToolTip id="tt51"><div class=CVariable>Port specific object files can be added here</div></div><div class=CToolTip id="tt52"><div class=CBuildTarget>Generate any files that are needed after actual build end. </div></div><div class=CToolTip id="tt53"><div class=CBuildTarget>Do platform specific after load stuff. </div></div><div class=CToolTip id="tt54"><div class=CBuildTarget>Do platform specific after run stuff. </div></div><div class=CToolTip id="tt55"><div class=CBuildTarget>Generate any files that are needed before actual build starts. </div></div><div class=CToolTip id="tt56"><div class=CBuildTarget>Do platform specific before load stuff. </div></div><div class=CToolTip id="tt57"><div class=CBuildTarget>Do platform specific after run stuff. </div></div><div class=CToolTip id="tt58"><div class=CVariable>Port specific source files can be added here</div></div><div class=CToolTip id="tt59"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_fini(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Target specific final code</div></div><div class=CToolTip id="tt60"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_free(</td><td class=PType nowrap>void&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Provide free() functionality in a platform specific way.</div></div><div class=CToolTip id="tt61"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_init(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>int&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argc,</td></tr><tr><td></td><td class=PType nowrap>char&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argv[]</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Target specific initialization code Test for some common mistakes.</div></div><div class=CToolTip id="tt62"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void *portable_malloc(</td><td class=PType nowrap>size_t&nbsp;</td><td class=PParameter nowrap>size</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Provide malloc() functionality in a platform specific way.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt63"><div class=CFile>Version: 1.01</div></div><div class=CToolTip id="tt64"><div class=CGeneric>How to report results on a data sheet?</div></div><div class=CToolTip id="tt65"><div class=CVariable>Define this flag if running does not consist of simple invocation of the binary. </div></div><div class=CToolTip id="tt66"><div class=CGeneric>What is and is not allowed.</div></div><!--END_ND_TOOLTIPS-->

</div><!--Index-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="../files/readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="../files/release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../files/core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex id=MSelected>Everything</div></div><div class=MEntry><div class=MIndex><a href="Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->


<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>