/* test_aes.c - TinyCrypt AES-128 tests (including NIST tests) */

/*
 *  Copyright (C) 2017 by Intel Corporation, All Rights Reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *    - Redistributions of source code must retain the above copyright notice,
 *     this list of conditions and the following disclaimer.
 *
 *    - Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 *    - Neither the name of Intel Corporation nor the names of its contributors
 *    may be used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 *  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 *  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 *  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 *  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 *  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 *  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 *  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 *  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *  POSSIBILITY OF SUCH DAMAGE.
 */

/*
 * DESCRIPTION
 * This module tests the following AES routines:
 *
 * Scenarios tested include:
 * - AES128 NIST key schedule test
 * - AES128 NIST encryption test
 * - AES128 NIST fixed-key and variable-text
 * - AES128 NIST variable-key and fixed-text
 */

#include <tinycrypt/aes.h>
#include <tinycrypt/constants.h>
#include <test_utils.h>

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include <stdint.h>
#include <stddef.h>

#define NUM_OF_NIST_KEYS 16
#define NUM_OF_FIXED_KEYS 128


struct kat_table {
	uint8_t in[NUM_OF_NIST_KEYS];
	uint8_t out[NUM_OF_NIST_KEYS];
};

/*
 * NIST test key schedule.
 */
int test_1(void)
{
	int result = TC_PASS;
	const uint8_t nist_key[NUM_OF_NIST_KEYS] = {
		0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
		0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
	};
	const struct tc_aes_key_sched_struct expected = {
		{
			0x2b7e1516, 0x28aed2a6, 0xabf71588, 0x09cf4f3c,
			0xa0fafe17, 0x88542cb1, 0x23a33939, 0x2a6c7605,
			0xf2c295f2, 0x7a96b943, 0x5935807a, 0x7359f67f,
			0x3d80477d, 0x4716fe3e, 0x1e237e44, 0x6d7a883b,
			0xef44a541, 0xa8525b7f, 0xb671253b, 0xdb0bad00,
			0xd4d1c6f8, 0x7c839d87, 0xcaf2b8bc, 0x11f915bc,
			0x6d88a37a, 0x110b3efd, 0xdbf98641, 0xca0093fd,
			0x4e54f70e, 0x5f5fc9f3, 0x84a64fb2, 0x4ea6dc4f,
			0xead27321, 0xb58dbad2, 0x312bf560, 0x7f8d292f,
			0xac7766f3, 0x19fadc21, 0x28d12941, 0x575c006e,
			0xd014f9a8, 0xc9ee2589, 0xe13f0cc8, 0xb6630ca6
		}
	};
	struct tc_aes_key_sched_struct s;

	TC_PRINT("AES128 %s (NIST key schedule test):\n", __func__);

	if (tc_aes128_set_encrypt_key(&s, nist_key) == 0) {
		TC_ERROR("AES128 test %s (NIST key schedule test) failed.\n",
			 __func__);
		result = TC_FAIL;
		goto exitTest1;
	}

	result = check_result(1, expected.words, sizeof(expected.words), s.words,
			      sizeof(s.words));

exitTest1:
	TC_END_RESULT(result);
	return result;
}

/*
 * NIST test vectors for encryption.
 */
int test_2(void)
{
	int result = TC_PASS;
	const uint8_t nist_key[NUM_OF_NIST_KEYS] = {
		0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
		0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
	};
	const uint8_t nist_input[NUM_OF_NIST_KEYS] = {
		0x32, 0x43, 0xf6, 0xa8, 0x88, 0x5a, 0x30, 0x8d,
		0x31, 0x31, 0x98, 0xa2, 0xe0, 0x37, 0x07, 0x34
	};
	const uint8_t expected[NUM_OF_NIST_KEYS] = {
		0x39, 0x25, 0x84, 0x1d, 0x02, 0xdc, 0x09, 0xfb,
		0xdc, 0x11, 0x85, 0x97, 0x19, 0x6a, 0x0b, 0x32
	};
	struct tc_aes_key_sched_struct s;
	uint8_t ciphertext[NUM_OF_NIST_KEYS];

	TC_PRINT("AES128 %s (NIST encryption test):\n", __func__);

	(void)tc_aes128_set_encrypt_key(&s, nist_key);
	if (tc_aes_encrypt(ciphertext, nist_input, &s) == 0) {
		TC_ERROR("AES128 %s (NIST encryption test) failed.\n",
			 __func__);
		result = TC_FAIL;
		goto exitTest2;
	}

	result = check_result(2, expected, sizeof(expected), ciphertext,
			      sizeof(ciphertext));

exitTest2:
	TC_END_RESULT(result);

	return result;
}

int var_text_test(unsigned int r, const uint8_t *in, const uint8_t *out,
		  TCAesKeySched_t s)
{
	uint8_t ciphertext[NUM_OF_NIST_KEYS];
	uint8_t decrypted[NUM_OF_NIST_KEYS];
	int result = TC_PASS;

	(void)tc_aes_encrypt(ciphertext, in, s);
	result = check_result(r, out, NUM_OF_NIST_KEYS, ciphertext,
			      sizeof(ciphertext));
	if (result != TC_FAIL) {
		if (tc_aes_decrypt(decrypted, ciphertext, s) == 0) {
			TC_ERROR("aes_decrypt failed\n");
			result = TC_FAIL;
		} else {
			result = check_result(r, in, NUM_OF_NIST_KEYS,
					      decrypted, sizeof(decrypted));
		}
	}

	return result;
}

/*
 * All NIST tests with fixed key and variable text.
 */
int test_3(void)
{
	int result = TC_PASS;
	const uint8_t key[NUM_OF_NIST_KEYS] = {
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	};
	const struct kat_table kat_tbl[NUM_OF_FIXED_KEYS] = {
		{{
				0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x3a, 0xd7, 0x8e, 0x72, 0x6c, 0x1e, 0xc0, 0x2b,
				0x7e, 0xbf, 0xe9, 0x2b, 0x23, 0xd9, 0xec, 0x34
			} },
		{{
				0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xaa, 0xe5, 0x93, 0x9c, 0x8e, 0xfd, 0xf2, 0xf0,
				0x4e, 0x60, 0xb9, 0xfe, 0x71, 0x17, 0xb2, 0xc2
			} },
		{{
				0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf0, 0x31, 0xd4, 0xd7, 0x4f, 0x5d, 0xcb, 0xf3,
				0x9d, 0xaa, 0xf8, 0xca, 0x3a, 0xf6, 0xe5, 0x27
			} },
		{{
				0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x96, 0xd9, 0xfd, 0x5c, 0xc4, 0xf0, 0x74, 0x41,
				0x72, 0x7d, 0xf0, 0xf3, 0x3e, 0x40, 0x1a, 0x36
			} },
		{{
				0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x30, 0xcc, 0xdb, 0x04, 0x46, 0x46, 0xd7, 0xe1,
				0xf3, 0xcc, 0xea, 0x3d, 0xca, 0x08, 0xb8, 0xc0
			} },
		{{
				0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x16, 0xae, 0x4c, 0xe5, 0x04, 0x2a, 0x67, 0xee,
				0x8e, 0x17, 0x7b, 0x7c, 0x58, 0x7e, 0xcc, 0x82
			} },
		{{
				0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb6, 0xda, 0x0b, 0xb1, 0x1a, 0x23, 0x85, 0x5d,
				0x9c, 0x5c, 0xb1, 0xb4, 0xc6, 0x41, 0x2e, 0x0a
			} },
		{{
				0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xdb, 0x4f, 0x1a, 0xa5, 0x30, 0x96, 0x7d, 0x67,
				0x32, 0xce, 0x47, 0x15, 0xeb, 0x0e, 0xe2, 0x4b
			} },
		{{
				0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xa8, 0x17, 0x38, 0x25, 0x26, 0x21, 0xdd, 0x18,
				0x0a, 0x34, 0xf3, 0x45, 0x5b, 0x4b, 0xaa, 0x2f
			} },
		{{
				0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x77, 0xe2, 0xb5, 0x08, 0xdb, 0x7f, 0xd8, 0x92,
				0x34, 0xca, 0xf7, 0x93, 0x9e, 0xe5, 0x62, 0x1a
			} },
		{{
				0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb8, 0x49, 0x9c, 0x25, 0x1f, 0x84, 0x42, 0xee,
				0x13, 0xf0, 0x93, 0x3b, 0x68, 0x8f, 0xcd, 0x19
			} },
		{{
				0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x96, 0x51, 0x35, 0xf8, 0xa8, 0x1f, 0x25, 0xc9,
				0xd6, 0x30, 0xb1, 0x75, 0x02, 0xf6, 0x8e, 0x53
			} },
		{{
				0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x8b, 0x87, 0x14, 0x5a, 0x01, 0xad, 0x1c, 0x6c,
				0xed, 0xe9, 0x95, 0xea, 0x36, 0x70, 0x45, 0x4f
			} },
		{{
				0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x8e, 0xae, 0x3b, 0x10, 0xa0, 0xc8, 0xca, 0x6d,
				0x1d, 0x3b, 0x0f, 0xa6, 0x1e, 0x56, 0xb0, 0xb2
			} },
		{{
				0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x64, 0xb4, 0xd6, 0x29, 0x81, 0x0f, 0xda, 0x6b,
				0xaf, 0xdf, 0x08, 0xf3, 0xb0, 0xd8, 0xd2, 0xc5
			} },
		{{
				0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xd7, 0xe5, 0xdb, 0xd3, 0x32, 0x45, 0x95, 0xf8,
				0xfd, 0xc7, 0xd7, 0xc5, 0x71, 0xda, 0x6c, 0x2a
			} },
		{{
				0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf3, 0xf7, 0x23, 0x75, 0x26, 0x4e, 0x16, 0x7f,
				0xca, 0x9d, 0xe2, 0xc1, 0x52, 0x7d, 0x96, 0x06
			} },
		{{
				0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x8e, 0xe7, 0x9d, 0xd4, 0xf4, 0x01, 0xff, 0x9b,
				0x7e, 0xa9, 0x45, 0xd8, 0x66, 0x66, 0xc1, 0x3b
			} },
		{{
				0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xdd, 0x35, 0xce, 0xa2, 0x79, 0x99, 0x40, 0xb4,
				0x0d, 0xb3, 0xf8, 0x19, 0xcb, 0x94, 0xc0, 0x8b
			} },
		{{
				0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x69, 0x41, 0xcb, 0x6b, 0x3e, 0x08, 0xc2, 0xb7,
				0xaf, 0xa5, 0x81, 0xeb, 0xdd, 0x60, 0x7b, 0x87
			} },
		{{
				0xff, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			},	{
				0x2c, 0x20, 0xf4, 0x39, 0xf6, 0xbb, 0x09, 0x7b,
				0x29, 0xb8, 0xbd, 0x6d, 0x99, 0xaa, 0xd7, 0x99
			} },
		{{
				0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x62, 0x5d, 0x01, 0xf0, 0x58, 0xe5, 0x65, 0xf7,
				0x7a, 0xe8, 0x63, 0x78, 0xbd, 0x2c, 0x49, 0xb3
			} },
		{{
				0xff, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xc0, 0xb5, 0xfd, 0x98, 0x19, 0x0e, 0xf4, 0x5f,
				0xbb, 0x43, 0x01, 0x43, 0x8d, 0x09, 0x59, 0x50
			} },
		{{
				0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x13, 0x00, 0x1f, 0xf5, 0xd9, 0x98, 0x06, 0xef,
				0xd2, 0x5d, 0xa3, 0x4f, 0x56, 0xbe, 0x85, 0x4b
			} },
		{{
				0xff, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x3b, 0x59, 0x4c, 0x60, 0xf5, 0xc8, 0x27, 0x7a,
				0x51, 0x13, 0x67, 0x7f, 0x94, 0x20, 0x8d, 0x82
			} },
		{{
				0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xe9, 0xc0, 0xfc, 0x18, 0x18, 0xe4, 0xaa, 0x46,
				0xbd, 0x2e, 0x39, 0xd6, 0x38, 0xf8, 0x9e, 0x05
			} },
		{{
				0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf8, 0x02, 0x3e, 0xe9, 0xc3, 0xfd, 0xc4, 0x5a,
				0x01, 0x9b, 0x4e, 0x98, 0x5c, 0x7e, 0x1a, 0x54
			} },
		{{
				0xff, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x35, 0xf4, 0x01, 0x82, 0xab, 0x46, 0x62, 0xf3,
				0x02, 0x3b, 0xae, 0xc1, 0xee, 0x79, 0x6b, 0x57
			} },
		{{
				0xff, 0xff, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x3a, 0xeb, 0xba, 0xd7, 0x30, 0x36, 0x49, 0xb4,
				0x19, 0x4a, 0x69, 0x45, 0xc6, 0xcc, 0x36, 0x94
			} },
		{{
				0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xa2, 0x12, 0x4b, 0xea, 0x53, 0xec, 0x28, 0x34,
				0x27, 0x9b, 0xed, 0x7f, 0x7e, 0xb0, 0xf9, 0x38
			} },
		{{
				0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb9, 0xfb, 0x43, 0x99, 0xfa, 0x4f, 0xac, 0xc7,
				0x30, 0x9e, 0x14, 0xec, 0x98, 0x36, 0x0b, 0x0a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xc2, 0x62, 0x77, 0x43, 0x74, 0x20, 0xc5, 0xd6,
				0x34, 0xf7, 0x15, 0xae, 0xa8, 0x1a, 0x91, 0x32
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x17, 0x1a, 0x0e, 0x1b, 0x2d, 0xd4, 0x24, 0xf0,
				0xe0, 0x89, 0xaf, 0x2c, 0x4c, 0x10, 0xf3, 0x2f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x7c, 0xad, 0xbe, 0x40, 0x2d, 0x1b, 0x20, 0x8f,
				0xe7, 0x35, 0xed, 0xce, 0x00, 0xae, 0xe7, 0xce
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x43, 0xb0, 0x2f, 0xf9, 0x29, 0xa1, 0x48, 0x5a,
				0xf6, 0xf5, 0xc6, 0xd6, 0x55, 0x8b, 0xaa, 0x0f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x09, 0x2f, 0xaa, 0xcc, 0x9b, 0xf4, 0x35, 0x08,
				0xbf, 0x8f, 0xa8, 0x61, 0x3c, 0xa7, 0x5d, 0xea
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xf8, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xcb, 0x2b, 0xf8, 0x28, 0x0f, 0x3f, 0x97, 0x42,
				0xc7, 0xed, 0x51, 0x3f, 0xe8, 0x02, 0x62, 0x9c
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x21, 0x5a, 0x41, 0xee, 0x44, 0x2f, 0xa9, 0x92,
				0xa6, 0xe3, 0x23, 0x98, 0x6d, 0xed, 0x3f, 0x68
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf2, 0x1e, 0x99, 0xcf, 0x4f, 0x0f, 0x77, 0xce,
				0xa8, 0x36, 0xe1, 0x1a, 0x2f, 0xe7, 0x5f, 0xb1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x95, 0xe3, 0xa0, 0xca, 0x90, 0x79, 0xe6, 0x46,
				0x33, 0x1d, 0xf8, 0xb4, 0xe7, 0x0d, 0x2c, 0xd6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x4a, 0xfe, 0x7f, 0x12, 0x0c, 0xe7, 0x61, 0x3f,
				0x74, 0xfc, 0x12, 0xa0, 0x1a, 0x82, 0x80, 0x73
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x82, 0x7f, 0x00, 0x0e, 0x75, 0xe2, 0xc8, 0xb9,
				0xd4, 0x79, 0xbe, 0xed, 0x91, 0x3f, 0xe6, 0x78
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x35, 0x83, 0x0c, 0x8e, 0x7a, 0xae, 0xfe, 0x2d,
				0x30, 0x31, 0x0e, 0xf3, 0x81, 0xcb, 0xf6, 0x91
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x19, 0x1a, 0xa0, 0xf2, 0xc8, 0x57, 0x01, 0x44,
				0xf3, 0x86, 0x57, 0xea, 0x40, 0x85, 0xeb, 0xe5
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x85, 0x06, 0x2c, 0x2c, 0x90, 0x9f, 0x15, 0xd9,
				0x26, 0x9b, 0x6c, 0x18, 0xce, 0x99, 0xc4, 0xf0
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x67, 0x80, 0x34, 0xdc, 0x9e, 0x41, 0xb5, 0xa5,
				0x60, 0xed, 0x23, 0x9e, 0xea, 0xb1, 0xbc, 0x78
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xc2, 0xf9, 0x3a, 0x4c, 0xe5, 0xab, 0x6d, 0x5d,
				0x56, 0xf1, 0xb9, 0x3c, 0xf1, 0x99, 0x11, 0xc1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x1c, 0x31, 0x12, 0xbc, 0xb0, 0xc1, 0xdc, 0xc7,
				0x49, 0xd7, 0x99, 0x74, 0x36, 0x91, 0xbf, 0x82
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x00, 0xc5, 0x5b, 0xd7, 0x5c, 0x7f, 0x9c, 0x88,
				0x19, 0x89, 0xd3, 0xec, 0x19, 0x11, 0xc0, 0xd4
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xea, 0x2e, 0x6b, 0x5e, 0xf1, 0x82, 0xb7, 0xdf,
				0xf3, 0x62, 0x9a, 0xbd, 0x6a, 0x12, 0x04, 0x5f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x22, 0x32, 0x23, 0x27, 0xe0, 0x17, 0x80, 0xb1,
				0x73, 0x97, 0xf2, 0x40, 0x87, 0xf8, 0xcc, 0x6f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xc9, 0xca, 0xcb, 0x5c, 0xd1, 0x16, 0x92, 0xc3,
				0x73, 0xb2, 0x41, 0x17, 0x68, 0x14, 0x9e, 0xe7
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xa1, 0x8e, 0x3d, 0xbb, 0xca, 0x57, 0x78, 0x60,
				0xda, 0xb6, 0xb8, 0x0d, 0xa3, 0x13, 0x92, 0x56
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x79, 0xb6, 0x1c, 0x37, 0xbf, 0x32, 0x8e, 0xcc,
				0xa8, 0xd7, 0x43, 0x26, 0x5a, 0x3d, 0x42, 0x5c
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xd2, 0xd9, 0x9c, 0x6b, 0xcc, 0x1f, 0x06, 0xfd,
				0xa8, 0xe2, 0x7e, 0x8a, 0xe3, 0xf1, 0xcc, 0xc7
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x1b, 0xfd, 0x4b, 0x91, 0xc7, 0x01, 0xfd, 0x6b,
				0x61, 0xb7, 0xf9, 0x97, 0x82, 0x9d, 0x66, 0x3b
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x11, 0x00, 0x5d, 0x52, 0xf2, 0x5f, 0x16, 0xbd,
				0xc9, 0x54, 0x5a, 0x87, 0x6a, 0x63, 0x49, 0x0a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x3a, 0x4d, 0x35, 0x4f, 0x02, 0xbb, 0x5a, 0x5e,
				0x47, 0xd3, 0x96, 0x66, 0x86, 0x7f, 0x24, 0x6a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xd4, 0x51, 0xb8, 0xd6, 0xe1, 0xe1, 0xa0, 0xeb,
				0xb1, 0x55, 0xfb, 0xbf, 0x6e, 0x7b, 0x7d, 0xc3
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x68, 0x98, 0xd4, 0xf4, 0x2f, 0xa7, 0xba, 0x6a,
				0x10, 0xac, 0x05, 0xe8, 0x7b, 0x9f, 0x20, 0x80
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb6, 0x11, 0x29, 0x5e, 0x73, 0x9c, 0xa7, 0xd9,
				0xb5, 0x0f, 0x8e, 0x4c, 0x0e, 0x75, 0x4a, 0x3f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x7d, 0x33, 0xfc, 0x7d, 0x8a, 0xbe, 0x3c, 0xa1,
				0x93, 0x67, 0x59, 0xf8, 0xf5, 0xde, 0xaf, 0x20
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x3b, 0x5e, 0x0f, 0x56, 0x6d, 0xc9, 0x6c, 0x29,
				0x8f, 0x0c, 0x12, 0x63, 0x75, 0x39, 0xb2, 0x5c
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf8, 0x07, 0xc3, 0xe7, 0x98, 0x5f, 0xe0, 0xf5,
				0xa5, 0x0e, 0x2c, 0xdb, 0x25, 0xc5, 0x10, 0x9e
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x41, 0xf9, 0x92, 0xa8, 0x56, 0xfb, 0x27, 0x8b,
				0x38, 0x9a, 0x62, 0xf5, 0xd2, 0x74, 0xd7, 0xe9
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x10, 0xd3, 0xed, 0x7a, 0x6f, 0xe1, 0x5a, 0xb4,
				0xd9, 0x1a, 0xcb, 0xc7, 0xd0, 0x76, 0x7a, 0xb1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x21, 0xfe, 0xec, 0xd4, 0x5b, 0x2e, 0x67, 0x59,
				0x73, 0xac, 0x33, 0xbf, 0x0c, 0x54, 0x24, 0xfc
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x14, 0x80, 0xcb, 0x39, 0x55, 0xba, 0x62, 0xd0,
				0x9e, 0xea, 0x66, 0x8f, 0x7c, 0x70, 0x88, 0x17
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x66, 0x40, 0x40, 0x33, 0xd6, 0xb7, 0x2b, 0x60,
				0x93, 0x54, 0xd5, 0x49, 0x6e, 0x7e, 0xb5, 0x11
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x1c, 0x31, 0x7a, 0x22, 0x0a, 0x7d, 0x70, 0x0d,
				0xa2, 0xb1, 0xe0, 0x75, 0xb0, 0x02, 0x66, 0xe1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xab, 0x3b, 0x89, 0x54, 0x22, 0x33, 0xf1, 0x27,
				0x1b, 0xf8, 0xfd, 0x0c, 0x0f, 0x40, 0x35, 0x45
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xd9, 0x3e, 0xae, 0x96, 0x6f, 0xac, 0x46, 0xdc,
				0xa9, 0x27, 0xd6, 0xb1, 0x14, 0xfa, 0x3f, 0x9e
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x1b, 0xde, 0xc5, 0x21, 0x31, 0x65, 0x03, 0xd9,
				0xd5, 0xee, 0x65, 0xdf, 0x3e, 0xa9, 0x4d, 0xdf
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xee, 0xf4, 0x56, 0x43, 0x1d, 0xea, 0x8b, 0x4a,
				0xcf, 0x83, 0xbd, 0xae, 0x37, 0x17, 0xf7, 0x5f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x06, 0xf2, 0x51, 0x9a, 0x2f, 0xaf, 0xaa, 0x59,
				0x6b, 0xfe, 0xf5, 0xcf, 0xa1, 0x5c, 0x21, 0xb9
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x25, 0x1a, 0x7e, 0xac, 0x7e, 0x2f, 0xe8, 0x09,
				0xe4, 0xaa, 0x8d, 0x0d, 0x70, 0x12, 0x53, 0x1a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x3b, 0xff, 0xc1, 0x6e, 0x4c, 0x49, 0xb2, 0x68,
				0xa2, 0x0f, 0x8d, 0x96, 0xa6, 0x0b, 0x40, 0x58
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xe8, 0x86, 0xf9, 0x28, 0x19, 0x99, 0xc5, 0xbb,
				0x3b, 0x3e, 0x88, 0x62, 0xe2, 0xf7, 0xc9, 0x88
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x56, 0x3b, 0xf9, 0x0d, 0x61, 0xbe, 0xef, 0x39,
				0xf4, 0x8d, 0xd6, 0x25, 0xfc, 0xef, 0x13, 0x61
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x4d, 0x37, 0xc8, 0x50, 0x64, 0x45, 0x63, 0xc6,
				0x9f, 0xd0, 0xac, 0xd9, 0xa0, 0x49, 0x32, 0x5b
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb8, 0x7c, 0x92, 0x1b, 0x91, 0x82, 0x9e, 0xf3,
				0xb1, 0x3c, 0xa5, 0x41, 0xee, 0x11, 0x30, 0xa6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x2e, 0x65, 0xeb, 0x6b, 0x6e, 0xa3, 0x83, 0xe1,
				0x09, 0xac, 0xcc, 0xe8, 0x32, 0x6b, 0x03, 0x93
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x9c, 0xa5, 0x47, 0xf7, 0x43, 0x9e, 0xdc, 0x3e,
				0x25, 0x5c, 0x0f, 0x4d, 0x49, 0xaa, 0x89, 0x90
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xa5, 0xe6, 0x52, 0x61, 0x4c, 0x93, 0x00, 0xf3,
				0x78, 0x16, 0xb1, 0xf9, 0xfd, 0x0c, 0x87, 0xf9
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x14, 0x95, 0x4f, 0x0b, 0x46, 0x97, 0x77, 0x6f,
				0x44, 0x49, 0x4f, 0xe4, 0x58, 0xd8, 0x14, 0xed
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x7c, 0x8d, 0x9a, 0xb6, 0xc2, 0x76, 0x17, 0x23,
				0xfe, 0x42, 0xf8, 0xbb, 0x50, 0x6c, 0xbc, 0xf7
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xdb, 0x7e, 0x19, 0x32, 0x67, 0x9f, 0xdd, 0x99,
				0x74, 0x2a, 0xab, 0x04, 0xaa, 0x0d, 0x5a, 0x80
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x4c, 0x6a, 0x1c, 0x83, 0xe5, 0x68, 0xcd, 0x10,
				0xf2, 0x7c, 0x2d, 0x73, 0xde, 0xd1, 0x9c, 0x28
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00
			}, {
				0x90, 0xec, 0xbe, 0x61, 0x77, 0xe6, 0x74, 0xc9,
				0x8d, 0xe4, 0x12, 0x41, 0x3f, 0x7a, 0xc9, 0x15
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00
			}, {
				0x90, 0x68, 0x4a, 0x2a, 0xc5, 0x5f, 0xe1, 0xec,
				0x2b, 0x8e, 0xbd, 0x56, 0x22, 0x52, 0x0b, 0x73
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00
			}, {
				0x74, 0x72, 0xf9, 0xa7, 0x98, 0x86, 0x07, 0xca,
				0x79, 0x70, 0x77, 0x95, 0x99, 0x10, 0x35, 0xe6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00
			}, {
				0x56, 0xaf, 0xf0, 0x89, 0x87, 0x8b, 0xf3, 0x35,
				0x2f, 0x8d, 0xf1, 0x72, 0xa3, 0xae, 0x47, 0xd8
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00
			}, {
				0x65, 0xc0, 0x52, 0x6c, 0xbe, 0x40, 0x16, 0x1b,
				0x80, 0x19, 0xa2, 0xa3, 0x17, 0x1a, 0xbd, 0x23
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00
			}, {
				0x37, 0x7b, 0xe0, 0xbe, 0x33, 0xb4, 0xe3, 0xe3,
				0x10, 0xb4, 0xaa, 0xbd, 0xa1, 0x73, 0xf8, 0x4f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00
			}, {
				0x94, 0x02, 0xe9, 0xaa, 0x6f, 0x69, 0xde, 0x65,
				0x04, 0xda, 0x8d, 0x20, 0xc4, 0xfc, 0xaa, 0x2f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00
			}, {
				0x12, 0x3c, 0x1f, 0x4a, 0xf3, 0x13, 0xad, 0x8c,
				0x2c, 0xe6, 0x48, 0xb2, 0xe7, 0x1f, 0xb6, 0xe1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00
			}, {
				0x1f, 0xfc, 0x62, 0x6d, 0x30, 0x20, 0x3d, 0xcd,
				0xb0, 0x01, 0x9f, 0xb8, 0x0f, 0x72, 0x6c, 0xf4
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00
			}, {
				0x76, 0xda, 0x1f, 0xbe, 0x3a, 0x50, 0x72, 0x8c,
				0x50, 0xfd, 0x2e, 0x62, 0x1b, 0x5a, 0xd8, 0x85
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00
			}, {
				0x08, 0x2e, 0xb8, 0xbe, 0x35, 0xf4, 0x42, 0xfb,
				0x52, 0x66, 0x8e, 0x16, 0xa5, 0x91, 0xd1, 0xd6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00
			}, {
				0xe6, 0x56, 0xf9, 0xec, 0xf5, 0xfe, 0x27, 0xec,
				0x3e, 0x4a, 0x73, 0xd0, 0x0c, 0x28, 0x2f, 0xb3
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xf8, 0x00, 0x00, 0x00
			}, {
				0x2c, 0xa8, 0x20, 0x9d, 0x63, 0x27, 0x4c, 0xd9,
				0xa2, 0x9b, 0xb7, 0x4b, 0xcd, 0x77, 0x68, 0x3a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00
			}, {
				0x79, 0xbf, 0x5d, 0xce, 0x14, 0xbb, 0x7d, 0xd7,
				0x3a, 0x8e, 0x36, 0x11, 0xde, 0x7c, 0xe0, 0x26
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0x00
			}, {
				0x3c, 0x84, 0x99, 0x39, 0xa5, 0xd2, 0x93, 0x99,
				0xf3, 0x44, 0xc4, 0xa0, 0xec, 0xa8, 0xa5, 0x76
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00
			}, {
				0xed, 0x3c, 0x0a, 0x94, 0xd5, 0x9b, 0xec, 0xe9,
				0x88, 0x35, 0xda, 0x7a, 0xa4, 0xf0, 0x7c, 0xa2
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x00, 0x00
			}, {
				0x63, 0x91, 0x9e, 0xd4, 0xce, 0x10, 0x19, 0x64,
				0x38, 0xb6, 0xad, 0x09, 0xd9, 0x9c, 0xd7, 0x95
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00
			}, {
				0x76, 0x78, 0xf3, 0xa8, 0x33, 0xf1, 0x9f, 0xea,
				0x95, 0xf3, 0xc6, 0x02, 0x9e, 0x2b, 0xc6, 0x10
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00
			}, {
				0x3a, 0xa4, 0x26, 0x83, 0x10, 0x67, 0xd3, 0x6b,
				0x92, 0xbe, 0x7c, 0x5f, 0x81, 0xc1, 0x3c, 0x56
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x00
			}, {
				0x92, 0x72, 0xe2, 0xd2, 0xcd, 0xd1, 0x10, 0x50,
				0x99, 0x8c, 0x84, 0x50, 0x77, 0xa3, 0x0e, 0xa0
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x00, 0x00
			}, {
				0x08, 0x8c, 0x4b, 0x53, 0xf5, 0xec, 0x0f, 0xf8,
				0x14, 0xc1, 0x9a, 0xda, 0xe7, 0xf6, 0x24, 0x6c
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00
			}, {
				0x40, 0x10, 0xa5, 0xe4, 0x01, 0xfd, 0xf0, 0xa0,
				0x35, 0x4d, 0xdb, 0xcc, 0x0d, 0x01, 0x2b, 0x17
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00
			}, {
				0xa8, 0x7a, 0x38, 0x57, 0x36, 0xc0, 0xa6, 0x18,
				0x9b, 0xd6, 0x58, 0x9b, 0xd8, 0x44, 0x5a, 0x93
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00
			}, {
				0x54, 0x5f, 0x2b, 0x83, 0xd9, 0x61, 0x6d, 0xcc,
				0xf6, 0x0f, 0xa9, 0x83, 0x0e, 0x9c, 0xd2, 0x87
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x00
			}, {
				0x4b, 0x70, 0x6f, 0x7f, 0x92, 0x40, 0x63, 0x52,
				0x39, 0x40, 0x37, 0xa6, 0xd4, 0xf4, 0x68, 0x8d
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00
			}, {
				0xb7, 0x97, 0x2b, 0x39, 0x41, 0xc4, 0x4b, 0x90,
				0xaf, 0xa7, 0xb2, 0x64, 0xbf, 0xba, 0x73, 0x87
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00
			}, {
				0x6f, 0x45, 0x73, 0x2c, 0xf1, 0x08, 0x81, 0x54,
				0x6f, 0x0f, 0xd2, 0x38, 0x96, 0xd2, 0xbb, 0x60
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00
			}, {
				0x2e, 0x35, 0x79, 0xca, 0x15, 0xaf, 0x27, 0xf6,
				0x4b, 0x3c, 0x95, 0x5a, 0x5b, 0xfc, 0x30, 0xba
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x00
			}, {
				0x34, 0xa2, 0xc5, 0xa9, 0x1a, 0xe2, 0xae, 0xc9,
				0x9b, 0x7d, 0x1b, 0x5f, 0xa6, 0x78, 0x04, 0x47
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00
			}, {
				0xa4, 0xd6, 0x61, 0x6b, 0xd0, 0x4f, 0x87, 0x33,
				0x5b, 0x0e, 0x53, 0x35, 0x12, 0x27, 0xa9, 0xee
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00
			}, {
				0x7f, 0x69, 0x2b, 0x03, 0x94, 0x58, 0x67, 0xd1,
				0x61, 0x79, 0xa8, 0xce, 0xfc, 0x83, 0xea, 0x3f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00
			}, {
				0x3b, 0xd1, 0x41, 0xee, 0x84, 0xa0, 0xe6, 0x41,
				0x4a, 0x26, 0xe7, 0xa4, 0xf2, 0x81, 0xf8, 0xa2
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80
			}, {
				0xd1, 0x78, 0x8f, 0x57, 0x2d, 0x98, 0xb2, 0xb1,
				0x6e, 0xc5, 0xd5, 0xf3, 0x92, 0x2b, 0x99, 0xbc
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0
			}, {
				0x08, 0x33, 0xff, 0x6f, 0x61, 0xd9, 0x8a, 0x57,
				0xb2, 0x88, 0xe8, 0xc3, 0x58, 0x6b, 0x85, 0xa6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0
			}, {
				0x85, 0x68, 0x26, 0x17, 0x97, 0xde, 0x17, 0x6b,
				0xf0, 0xb4, 0x3b, 0xec, 0xc6, 0x28, 0x5a, 0xfb
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0
			}, {
				0xf9, 0xb0, 0xfd, 0xa0, 0xc4, 0xa8, 0x98, 0xf5,
				0xb9, 0xe6, 0xf6, 0x61, 0xc4, 0xce, 0x4d, 0x07
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8
			}, {
				0x8a, 0xde, 0x89, 0x59, 0x13, 0x68, 0x5c, 0x67,
				0xc5, 0x26, 0x9f, 0x8a, 0xae, 0x42, 0x98, 0x3e
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc
			}, {
				0x39, 0xbd, 0xe6, 0x7d, 0x5c, 0x8e, 0xd8, 0xa8,
				0xb1, 0xc3, 0x7e, 0xb8, 0xfa, 0x9f, 0x5a, 0xc0
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe
			}, {
				0x5c, 0x00, 0x5e, 0x72, 0xc1, 0x41, 0x8c, 0x44,
				0xf5, 0x69, 0xf2, 0xea, 0x33, 0xba, 0x54, 0xf3
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
			}, {
				0x3f, 0x5b, 0x8c, 0xc9, 0xea, 0x85, 0x5a, 0x0a,
				0xfa, 0x73, 0x47, 0xd2, 0x3e, 0x8d, 0x66, 0x4e
			} }
	};
	struct tc_aes_key_sched_struct s;
	unsigned int i;

	TC_PRINT("AES128 %s (NIST fixed-key and variable-text):\n", __func__);

	(void)tc_aes128_set_encrypt_key(&s, key);

	for (i = 0; i < 128; ++i) {
		result = var_text_test(i, kat_tbl[i].in, kat_tbl[i].out, &s);
		if (result == TC_FAIL) {
			break;
		}
	}

	TC_END_RESULT(result);

	return result;
}

int var_key_test(unsigned int r, const uint8_t *in, const uint8_t *out)
{
	int result = TC_PASS;

	const uint8_t plaintext[NUM_OF_NIST_KEYS] = {
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	};
	uint8_t ciphertext[NUM_OF_NIST_KEYS];
	struct tc_aes_key_sched_struct s;

	(void)tc_aes128_set_encrypt_key(&s, in);

	(void)tc_aes_encrypt(ciphertext, plaintext, &s);
	result = check_result(r, out, NUM_OF_NIST_KEYS, ciphertext,
			      sizeof(ciphertext));

	return result;
}

/*
 * All NIST tests with variable key and fixed text.
 */
int test_4(void)
{
	int result = TC_PASS;
	const struct kat_table kat_tbl[NUM_OF_FIXED_KEYS] = {
		{{
				0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x0e, 0xdd, 0x33, 0xd3, 0xc6, 0x21, 0xe5, 0x46,
				0x45, 0x5b, 0xd8, 0xba, 0x14, 0x18, 0xbe, 0xc8
			} },
		{{
				0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x4b, 0xc3, 0xf8, 0x83, 0x45, 0x0c, 0x11, 0x3c,
				0x64, 0xca, 0x42, 0xe1, 0x11, 0x2a, 0x9e, 0x87
			} },
		{{
				0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x72, 0xa1, 0xda, 0x77, 0x0f, 0x5d, 0x7a, 0xc4,
				0xc9, 0xef, 0x94, 0xd8, 0x22, 0xaf, 0xfd, 0x97
			} },
		{{
				0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x97, 0x00, 0x14, 0xd6, 0x34, 0xe2, 0xb7, 0x65,
				0x07, 0x77, 0xe8, 0xe8, 0x4d, 0x03, 0xcc, 0xd8
			} },
		{{
				0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf1, 0x7e, 0x79, 0xae, 0xd0, 0xdb, 0x7e, 0x27,
				0x9e, 0x95, 0x5b, 0x5f, 0x49, 0x38, 0x75, 0xa7
			} },
		{{
				0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x9e, 0xd5, 0xa7, 0x51, 0x36, 0xa9, 0x40, 0xd0,
				0x96, 0x3d, 0xa3, 0x79, 0xdb, 0x4a, 0xf2, 0x6a
			} },
		{{
				0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xc4, 0x29, 0x5f, 0x83, 0x46, 0x5c, 0x77, 0x55,
				0xe8, 0xfa, 0x36, 0x4b, 0xac, 0x6a, 0x7e, 0xa5
			} },
		{{
				0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb1, 0xd7, 0x58, 0x25, 0x6b, 0x28, 0xfd, 0x85,
				0x0a, 0xd4, 0x94, 0x42, 0x08, 0xcf, 0x11, 0x55
			} },
		{{
				0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x42, 0xff, 0xb3, 0x4c, 0x74, 0x3d, 0xe4, 0xd8,
				0x8c, 0xa3, 0x80, 0x11, 0xc9, 0x90, 0x89, 0x0b
			} },
		{{
				0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x99, 0x58, 0xf0, 0xec, 0xea, 0x8b, 0x21, 0x72,
				0xc0, 0xc1, 0x99, 0x5f, 0x91, 0x82, 0xc0, 0xf3
			} },
		{{
				0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x95, 0x6d, 0x77, 0x98, 0xfa, 0xc2, 0x0f, 0x82,
				0xa8, 0x82, 0x3f, 0x98, 0x4d, 0x06, 0xf7, 0xf5
			} },
		{{
				0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xa0, 0x1b, 0xf4, 0x4f, 0x2d, 0x16, 0xbe, 0x92,
				0x8c, 0xa4, 0x4a, 0xaf, 0x7b, 0x9b, 0x10, 0x6b
			} },
		{{
				0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb5, 0xf1, 0xa3, 0x3e, 0x50, 0xd4, 0x0d, 0x10,
				0x37, 0x64, 0xc7, 0x6b, 0xd4, 0xc6, 0xb6, 0xf8
			} },
		{{
				0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x26, 0x37, 0x05, 0x0c, 0x9f, 0xc0, 0xd4, 0x81,
				0x7e, 0x2d, 0x69, 0xde, 0x87, 0x8a, 0xee, 0x8d
			} },
		{{
				0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x11, 0x3e, 0xcb, 0xe4, 0xa4, 0x53, 0x26, 0x9a,
				0x0d, 0xd2, 0x60, 0x69, 0x46, 0x7f, 0xb5, 0xb5
			} },
		{{
				0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x97, 0xd0, 0x75, 0x4f, 0xe6, 0x8f, 0x11, 0xb9,
				0xe3, 0x75, 0xd0, 0x70, 0xa6, 0x08, 0xc8, 0x84
			} },
		{{
				0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xc6, 0xa0, 0xb3, 0xe9, 0x98, 0xd0, 0x50, 0x68,
				0xa5, 0x39, 0x97, 0x78, 0x40, 0x52, 0x00, 0xb4
			} },
		{{
				0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xdf, 0x55, 0x6a, 0x33, 0x43, 0x8d, 0xb8, 0x7b,
				0xc4, 0x1b, 0x17, 0x52, 0xc5, 0x5e, 0x5e, 0x49
			} },
		{{
				0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x90, 0xfb, 0x12, 0x8d, 0x3a, 0x1a, 0xf6, 0xe5,
				0x48, 0x52, 0x1b, 0xb9, 0x62, 0xbf, 0x1f, 0x05
			} },
		{{
				0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x26, 0x29, 0x8e, 0x9c, 0x1d, 0xb5, 0x17, 0xc2,
				0x15, 0xfa, 0xdf, 0xb7, 0xd2, 0xa8, 0xd6, 0x91
			} },
		{{
				0xff, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xa6, 0xcb, 0x76, 0x1d, 0x61, 0xf8, 0x29, 0x2d,
				0x0d, 0xf3, 0x93, 0xa2, 0x79, 0xad, 0x03, 0x80
			} },
		{{
				0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x12, 0xac, 0xd8, 0x9b, 0x13, 0xcd, 0x5f, 0x87,
				0x26, 0xe3, 0x4d, 0x44, 0xfd, 0x48, 0x61, 0x08
			} },
		{{
				0xff, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x95, 0xb1, 0x70, 0x3f, 0xc5, 0x7b, 0xa0, 0x9f,
				0xe0, 0xc3, 0x58, 0x0f, 0xeb, 0xdd, 0x7e, 0xd4
			} },
		{{
				0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xde, 0x11, 0x72, 0x2d, 0x89, 0x3e, 0x9f, 0x91,
				0x21, 0xc3, 0x81, 0xbe, 0xcc, 0x1d, 0xa5, 0x9a
			} },
		{{
				0xff, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x6d, 0x11, 0x4c, 0xcb, 0x27, 0xbf, 0x39, 0x10,
				0x12, 0xe8, 0x97, 0x4c, 0x54, 0x6d, 0x9b, 0xf2
			} },
		{{
				0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x5c, 0xe3, 0x7e, 0x17, 0xeb, 0x46, 0x46, 0xec,
				0xfa, 0xc2, 0x9b, 0x9c, 0xc3, 0x8d, 0x93, 0x40
			} },
		{{
				0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x18, 0xc1, 0xb6, 0xe2, 0x15, 0x71, 0x22, 0x05,
				0x6d, 0x02, 0x43, 0xd8, 0xa1, 0x65, 0xcd, 0xdb
			} },
		{{
				0xff, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x99, 0x69, 0x3e, 0x6a, 0x59, 0xd1, 0x36, 0x6c,
				0x74, 0xd8, 0x23, 0x56, 0x2d, 0x7e, 0x14, 0x31
			} },
		{{
				0xff, 0xff, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x6c, 0x7c, 0x64, 0xdc, 0x84, 0xa8, 0xbb, 0xa7,
				0x58, 0xed, 0x17, 0xeb, 0x02, 0x5a, 0x57, 0xe3
			} },
		{{
				0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xe1, 0x7b, 0xc7, 0x9f, 0x30, 0xea, 0xab, 0x2f,
				0xac, 0x2c, 0xbb, 0xe3, 0x45, 0x8d, 0x68, 0x7a
			} },
		{{
				0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x11, 0x14, 0xbc, 0x20, 0x28, 0x00, 0x9b, 0x92,
				0x3f, 0x0b, 0x01, 0x91, 0x5c, 0xe5, 0xe7, 0xc4
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x9c, 0x28, 0x52, 0x4a, 0x16, 0xa1, 0xe1, 0xc1,
				0x45, 0x29, 0x71, 0xca, 0xa8, 0xd1, 0x34, 0x76
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xed, 0x62, 0xe1, 0x63, 0x63, 0x63, 0x83, 0x60,
				0xfd, 0xd6, 0xad, 0x62, 0x11, 0x27, 0x94, 0xf0
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x5a, 0x86, 0x88, 0xf0, 0xb2, 0xa2, 0xc1, 0x62,
				0x24, 0xc1, 0x61, 0x65, 0x8f, 0xfd, 0x40, 0x44
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x23, 0xf7, 0x10, 0x84, 0x2b, 0x9b, 0xb9, 0xc3,
				0x2f, 0x26, 0x64, 0x8c, 0x78, 0x68, 0x07, 0xca
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x44, 0xa9, 0x8b, 0xf1, 0x1e, 0x16, 0x3f, 0x63,
				0x2c, 0x47, 0xec, 0x6a, 0x49, 0x68, 0x3a, 0x89
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xf8, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x0f, 0x18, 0xaf, 0xf9, 0x42, 0x74, 0x69, 0x6d,
				0x9b, 0x61, 0x84, 0x8b, 0xd5, 0x0a, 0xc5, 0xe5
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x82, 0x40, 0x85, 0x71, 0xc3, 0xe2, 0x42, 0x45,
				0x40, 0x20, 0x7f, 0x83, 0x3b, 0x6d, 0xda, 0x69
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x30, 0x3f, 0xf9, 0x96, 0x94, 0x7f, 0x0c, 0x7d,
				0x1f, 0x43, 0xc8, 0xf3, 0x02, 0x7b, 0x9b, 0x75
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x7d, 0xf4, 0xda, 0xf4, 0xad, 0x29, 0xa3, 0x61,
				0x5a, 0x9b, 0x6e, 0xce, 0x5c, 0x99, 0x51, 0x8a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xc7, 0x29, 0x54, 0xa4, 0x8d, 0x07, 0x74, 0xdb,
				0x0b, 0x49, 0x71, 0xc5, 0x26, 0x26, 0x04, 0x15
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x1d, 0xf9, 0xb7, 0x61, 0x12, 0xdc, 0x65, 0x31,
				0xe0, 0x7d, 0x2c, 0xfd, 0xa0, 0x44, 0x11, 0xf0
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x8e, 0x4d, 0x8e, 0x69, 0x91, 0x19, 0xe1, 0xfc,
				0x87, 0x54, 0x5a, 0x64, 0x7f, 0xb1, 0xd3, 0x4f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xe6, 0xc4, 0x80, 0x7a, 0xe1, 0x1f, 0x36, 0xf0,
				0x91, 0xc5, 0x7d, 0x9f, 0xb6, 0x85, 0x48, 0xd1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x8e, 0xbf, 0x73, 0xaa, 0xd4, 0x9c, 0x82, 0x00,
				0x7f, 0x77, 0xa5, 0xc1, 0xcc, 0xec, 0x6a, 0xb4
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x4f, 0xb2, 0x88, 0xcc, 0x20, 0x40, 0x04, 0x90,
				0x01, 0xd2, 0xc7, 0x58, 0x5a, 0xd1, 0x23, 0xfc
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x04, 0x49, 0x71, 0x10, 0xef, 0xb9, 0xdc, 0xeb,
				0x13, 0xe2, 0xb1, 0x3f, 0xb4, 0x46, 0x55, 0x64
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x75, 0x55, 0x0e, 0x6c, 0xb5, 0xa8, 0x8e, 0x49,
				0x63, 0x4c, 0x9a, 0xb6, 0x9e, 0xda, 0x04, 0x30
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb6, 0x76, 0x84, 0x73, 0xce, 0x98, 0x43, 0xea,
				0x66, 0xa8, 0x14, 0x05, 0xdd, 0x50, 0xb3, 0x45
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xcb, 0x2f, 0x43, 0x03, 0x83, 0xf9, 0x08, 0x4e,
				0x03, 0xa6, 0x53, 0x57, 0x1e, 0x06, 0x5d, 0xe6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xff, 0x4e, 0x66, 0xc0, 0x7b, 0xae, 0x3e, 0x79,
				0xfb, 0x7d, 0x21, 0x08, 0x47, 0xa3, 0xb0, 0xba
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x7b, 0x90, 0x78, 0x51, 0x25, 0x50, 0x5f, 0xad,
				0x59, 0xb1, 0x3c, 0x18, 0x6d, 0xd6, 0x6c, 0xe3
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x8b, 0x52, 0x7a, 0x6a, 0xeb, 0xda, 0xec, 0x9e,
				0xae, 0xf8, 0xed, 0xa2, 0xcb, 0x77, 0x83, 0xe5
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x43, 0xfd, 0xaf, 0x53, 0xeb, 0xbc, 0x98, 0x80,
				0xc2, 0x28, 0x61, 0x7d, 0x6a, 0x9b, 0x54, 0x8b
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x53, 0x78, 0x61, 0x04, 0xb9, 0x74, 0x4b, 0x98,
				0xf0, 0x52, 0xc4, 0x6f, 0x1c, 0x85, 0x0d, 0x0b
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xb5, 0xab, 0x30, 0x13, 0xdd, 0x1e, 0x61, 0xdf,
				0x06, 0xcb, 0xaf, 0x34, 0xca, 0x2a, 0xee, 0x78
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x74, 0x70, 0x46, 0x9b, 0xe9, 0x72, 0x30, 0x30,
				0xfd, 0xcc, 0x73, 0xa8, 0xcd, 0x4f, 0xbb, 0x10
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xa3, 0x5a, 0x63, 0xf5, 0x34, 0x3e, 0xbe, 0x9e,
				0xf8, 0x16, 0x7b, 0xcb, 0x48, 0xad, 0x12, 0x2e
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xfd, 0x86, 0x87, 0xf0, 0x75, 0x7a, 0x21, 0x0e,
				0x9f, 0xdf, 0x18, 0x12, 0x04, 0xc3, 0x08, 0x63
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x7a, 0x18, 0x1e, 0x84, 0xbd, 0x54, 0x57, 0xd2,
				0x6a, 0x88, 0xfb, 0xae, 0x96, 0x01, 0x8f, 0xb0
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x65, 0x33, 0x17, 0xb9, 0x36, 0x2b, 0x6f, 0x9b,
				0x9e, 0x1a, 0x58, 0x0e, 0x68, 0xd4, 0x94, 0xb5
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x99, 0x5c, 0x9d, 0xc0, 0xb6, 0x89, 0xf0, 0x3c,
				0x45, 0x86, 0x7b, 0x5f, 0xaa, 0x5c, 0x18, 0xd1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x77, 0xa4, 0xd9, 0x6d, 0x56, 0xdd, 0xa3, 0x98,
				0xb9, 0xaa, 0xbe, 0xcf, 0xc7, 0x57, 0x29, 0xfd
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x84, 0xbe, 0x19, 0xe0, 0x53, 0x63, 0x5f, 0x09,
				0xf2, 0x66, 0x5e, 0x7b, 0xae, 0x85, 0xb4, 0x2d
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x32, 0xcd, 0x65, 0x28, 0x42, 0x92, 0x6a, 0xea,
				0x4a, 0xa6, 0x13, 0x7b, 0xb2, 0xbe, 0x2b, 0x5e
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x49, 0x3d, 0x4a, 0x4f, 0x38, 0xeb, 0xb3, 0x37,
				0xd1, 0x0a, 0xa8, 0x4e, 0x91, 0x71, 0xa5, 0x54
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xd9, 0xbf, 0xf7, 0xff, 0x45, 0x4b, 0x0e, 0xc5,
				0xa4, 0xa2, 0xa6, 0x95, 0x66, 0xe2, 0xcb, 0x84
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x35, 0x35, 0xd5, 0x65, 0xac, 0xe3, 0xf3, 0x1e,
				0xb2, 0x49, 0xba, 0x2c, 0xc6, 0x76, 0x5d, 0x7a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf6, 0x0e, 0x91, 0xfc, 0x32, 0x69, 0xee, 0xcf,
				0x32, 0x31, 0xc6, 0xe9, 0x94, 0x56, 0x97, 0xc6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xab, 0x69, 0xcf, 0xad, 0xf5, 0x1f, 0x8e, 0x60,
				0x4d, 0x9c, 0xc3, 0x71, 0x82, 0xf6, 0x63, 0x5a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x78, 0x66, 0x37, 0x3f, 0x24, 0xa0, 0xb6, 0xed,
				0x56, 0xe0, 0xd9, 0x6f, 0xcd, 0xaf, 0xb8, 0x77
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x1e, 0xa4, 0x48, 0xc2, 0xaa, 0xc9, 0x54, 0xf5,
				0xd8, 0x12, 0xe9, 0xd7, 0x84, 0x94, 0x44, 0x6a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xac, 0xc5, 0x59, 0x9d, 0xd8, 0xac, 0x02, 0x23,
				0x9a, 0x0f, 0xef, 0x4a, 0x36, 0xdd, 0x16, 0x68
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xd8, 0x76, 0x44, 0x68, 0xbb, 0x10, 0x38, 0x28,
				0xcf, 0x7e, 0x14, 0x73, 0xce, 0x89, 0x50, 0x73
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x1b, 0x0d, 0x02, 0x89, 0x36, 0x83, 0xb9, 0xf1,
				0x80, 0x45, 0x8e, 0x4a, 0xa6, 0xb7, 0x39, 0x82
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x96, 0xd9, 0xb0, 0x17, 0xd3, 0x02, 0xdf, 0x41,
				0x0a, 0x93, 0x7d, 0xcd, 0xb8, 0xbb, 0x6e, 0x43
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xef, 0x16, 0x23, 0xcc, 0x44, 0x31, 0x3c, 0xff,
				0x44, 0x0b, 0x15, 0x94, 0xa7, 0xe2, 0x1c, 0xc6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x28, 0x4c, 0xa2, 0xfa, 0x35, 0x80, 0x7b, 0x8b,
				0x0a, 0xe4, 0xd1, 0x9e, 0x11, 0xd7, 0xdb, 0xd7
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf2, 0xe9, 0x76, 0x87, 0x57, 0x55, 0xf9, 0x40,
				0x1d, 0x54, 0xf3, 0x6e, 0x2a, 0x23, 0xa5, 0x94
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xec, 0x19, 0x8a, 0x18, 0xe1, 0x0e, 0x53, 0x24,
				0x03, 0xb7, 0xe2, 0x08, 0x87, 0xc8, 0xdd, 0x80
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x54, 0x5d, 0x50, 0xeb, 0xd9, 0x19, 0xe4, 0xa6,
				0x94, 0x9d, 0x96, 0xad, 0x47, 0xe4, 0x6a, 0x80
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xdb, 0xdf, 0xb5, 0x27, 0x06, 0x0e, 0x0a, 0x71,
				0x00, 0x9c, 0x7b, 0xb0, 0xc6, 0x8f, 0x1d, 0x44
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x9c, 0xfa, 0x13, 0x22, 0xea, 0x33, 0xda, 0x21,
				0x73, 0xa0, 0x24, 0xf2, 0xff, 0x0d, 0x89, 0x6d
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x87, 0x85, 0xb1, 0xa7, 0x5b, 0x0f, 0x3b, 0xd9,
				0x58, 0xdc, 0xd0, 0xe2, 0x93, 0x18, 0xc5, 0x21
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x38, 0xf6, 0x7b, 0x9e, 0x98, 0xe4, 0xa9, 0x7b,
				0x6d, 0xf0, 0x30, 0xa9, 0xfc, 0xdd, 0x01, 0x04
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x19, 0x2a, 0xff, 0xfb, 0x2c, 0x88, 0x0e, 0x82,
				0xb0, 0x59, 0x26, 0xd0, 0xfc, 0x6c, 0x44, 0x8b
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0x6a, 0x79, 0x80, 0xce, 0x7b, 0x10, 0x5c, 0xf5,
				0x30, 0x95, 0x2d, 0x74, 0xda, 0xaf, 0x79, 0x8c
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00
			}, {
				0xea, 0x36, 0x95, 0xe1, 0x35, 0x1b, 0x9d, 0x68,
				0x58, 0xbd, 0x95, 0x8c, 0xf5, 0x13, 0xef, 0x6c
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00
			}, {
				0x6d, 0xa0, 0x49, 0x0b, 0xa0, 0xba, 0x03, 0x43,
				0xb9, 0x35, 0x68, 0x1d, 0x2c, 0xce, 0x5b, 0xa1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf0, 0xea, 0x23, 0xaf, 0x08, 0x53, 0x40, 0x11,
				0xc6, 0x00, 0x09, 0xab, 0x29, 0xad, 0xa2, 0xf1
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00
			}, {
				0xff, 0x13, 0x80, 0x6c, 0xf1, 0x9c, 0xc3, 0x87,
				0x21, 0x55, 0x4d, 0x7c, 0x0f, 0xcd, 0xcd, 0x4b
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00
			}, {
				0x68, 0x38, 0xaf, 0x1f, 0x4f, 0x69, 0xba, 0xe9,
				0xd8, 0x5d, 0xd1, 0x88, 0xdc, 0xdf, 0x06, 0x88
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00
			}, {
				0x36, 0xcf, 0x44, 0xc9, 0x2d, 0x55, 0x0b, 0xfb,
				0x1e, 0xd2, 0x8e, 0xf5, 0x83, 0xdd, 0xf5, 0xd7
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00
			}, {
				0xd0, 0x6e, 0x31, 0x95, 0xb5, 0x37, 0x6f, 0x10,
				0x9d, 0x5c, 0x4e, 0xc6, 0xc5, 0xd6, 0x2c, 0xed
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00
			}, {
				0xc4, 0x40, 0xde, 0x01, 0x4d, 0x3d, 0x61, 0x07,
				0x07, 0x27, 0x9b, 0x13, 0x24, 0x2a, 0x5c, 0x36
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00
			}, {
				0xf0, 0xc5, 0xc6, 0xff, 0xa5, 0xe0, 0xbd, 0x3a,
				0x94, 0xc8, 0x8f, 0x6b, 0x6f, 0x7c, 0x16, 0xb9
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00
			}, {
				0x3e, 0x40, 0xc3, 0x90, 0x1c, 0xd7, 0xef, 0xfc,
				0x22, 0xbf, 0xfc, 0x35, 0xde, 0xe0, 0xb4, 0xd9
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00, 0x00
			}, {
				0xb6, 0x33, 0x05, 0xc7, 0x2b, 0xed, 0xfa, 0xb9,
				0x73, 0x82, 0xc4, 0x06, 0xd0, 0xc4, 0x9b, 0xc6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00
			}, {
				0x36, 0xbb, 0xaa, 0xb2, 0x2a, 0x6b, 0xd4, 0x92,
				0x5a, 0x99, 0xa2, 0xb4, 0x08, 0xd2, 0xdb, 0xae
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00
			}, {
				0x30, 0x7c, 0x5b, 0x8f, 0xcd, 0x05, 0x33, 0xab,
				0x98, 0xbc, 0x51, 0xe2, 0x7a, 0x6c, 0xe4, 0x61
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xf8, 0x00, 0x00, 0x00
			}, {
				0x82, 0x9c, 0x04, 0xff, 0x4c, 0x07, 0x51, 0x3c,
				0x0b, 0x3e, 0xf0, 0x5c, 0x03, 0xe3, 0x37, 0xb5
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00
			}, {
				0xf1, 0x7a, 0xf0, 0xe8, 0x95, 0xdd, 0xa5, 0xeb,
				0x98, 0xef, 0xc6, 0x80, 0x66, 0xe8, 0x4c, 0x54
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0x00
			}, {
				0x27, 0x71, 0x67, 0xf3, 0x81, 0x2a, 0xff, 0xf1,
				0xff, 0xac, 0xb4, 0xa9, 0x34, 0x37, 0x9f, 0xc3
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00
			}, {
				0x2c, 0xb1, 0xdc, 0x3a, 0x9c, 0x72, 0x97, 0x2e,
				0x42, 0x5a, 0xe2, 0xef, 0x3e, 0xb5, 0x97, 0xcd
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x00, 0x00
			}, {
				0x36, 0xae, 0xaa, 0x3a, 0x21, 0x3e, 0x96, 0x8d,
				0x4b, 0x5b, 0x67, 0x9d, 0x3a, 0x2c, 0x97, 0xfe
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x00
			}, {
				0x92, 0x41, 0xda, 0xca, 0x4f, 0xdd, 0x03, 0x4a,
				0x82, 0x37, 0x2d, 0xb5, 0x0e, 0x1a, 0x0f, 0x3f
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00
			}, {
				0xc1, 0x45, 0x74, 0xd9, 0xcd, 0x00, 0xcf, 0x2b,
				0x5a, 0x7f, 0x77, 0xe5, 0x3c, 0xd5, 0x78, 0x85
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x00
			}, {
				0x79, 0x3d, 0xe3, 0x92, 0x36, 0x57, 0x0a, 0xba,
				0x83, 0xab, 0x9b, 0x73, 0x7c, 0xb5, 0x21, 0xc9
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x00, 0x00
			}, {
				0x16, 0x59, 0x1c, 0x0f, 0x27, 0xd6, 0x0e, 0x29,
				0xb8, 0x5a, 0x96, 0xc3, 0x38, 0x61, 0xa7, 0xef
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00
			}, {
				0x44, 0xfb, 0x5c, 0x4d, 0x4f, 0x5c, 0xb7, 0x9b,
				0xe5, 0xc1, 0x74, 0xa3, 0xb1, 0xc9, 0x73, 0x48
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00
			}, {
				0x67, 0x4d, 0x2b, 0x61, 0x63, 0x3d, 0x16, 0x2b,
				0xe5, 0x9d, 0xde, 0x04, 0x22, 0x2f, 0x47, 0x40
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00
			}, {
				0xb4, 0x75, 0x0f, 0xf2, 0x63, 0xa6, 0x5e, 0x1f,
				0x9e, 0x92, 0x4c, 0xcf, 0xd9, 0x8f, 0x3e, 0x37
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x00
			}, {
				0x62, 0xd0, 0x66, 0x2d, 0x6e, 0xae, 0xdd, 0xed,
				0xeb, 0xae, 0x7f, 0x7e, 0xa3, 0xa4, 0xf6, 0xb6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00
			}, {
				0x70, 0xc4, 0x6b, 0xb3, 0x06, 0x92, 0xbe, 0x65,
				0x7f, 0x7e, 0xaa, 0x93, 0xeb, 0xad, 0x98, 0x97
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00
			}, {
				0x32, 0x39, 0x94, 0xcf, 0xb9, 0xda, 0x28, 0x5a,
				0x5d, 0x96, 0x42, 0xe1, 0x75, 0x9b, 0x22, 0x4a
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00
			}, {
				0x1d, 0xbf, 0x57, 0x87, 0x7b, 0x7b, 0x17, 0x38,
				0x5c, 0x85, 0xd0, 0xb5, 0x48, 0x51, 0xe3, 0x71
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x00
			}, {
				0xdf, 0xa5, 0xc0, 0x97, 0xcd, 0xc1, 0x53, 0x2a,
				0xc0, 0x71, 0xd5, 0x7b, 0x1d, 0x28, 0xd1, 0xbd
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00
			}, {
				0x3a, 0x0c, 0x53, 0xfa, 0x37, 0x31, 0x1f, 0xc1,
				0x0b, 0xd2, 0xa9, 0x98, 0x1f, 0x51, 0x31, 0x74
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00
			}, {
				0xba, 0x4f, 0x97, 0x0c, 0x0a, 0x25, 0xc4, 0x18,
				0x14, 0xbd, 0xae, 0x2e, 0x50, 0x6b, 0xe3, 0xb4
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00
			}, {
				0x2d, 0xce, 0x3a, 0xcb, 0x72, 0x7c, 0xd1, 0x3c,
				0xcd, 0x76, 0xd4, 0x25, 0xea, 0x56, 0xe4, 0xf6
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80
			}, {
				0x51, 0x60, 0x47, 0x4d, 0x50, 0x4b, 0x9b, 0x3e,
				0xef, 0xb6, 0x8d, 0x35, 0xf2, 0x45, 0xf4, 0xb3
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0
			}, {
				0x41, 0xa8, 0xa9, 0x47, 0x76, 0x66, 0x35, 0xde,
				0xc3, 0x75, 0x53, 0xd9, 0xa6, 0xc0, 0xcb, 0xb7
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0
			}, {
				0x25, 0xd6, 0xcf, 0xe6, 0x88, 0x1f, 0x2b, 0xf4,
				0x97, 0xdd, 0x14, 0xcd, 0x4d, 0xdf, 0x44, 0x5b
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0
			}, {
				0x41, 0xc7, 0x8c, 0x13, 0x5e, 0xd9, 0xe9, 0x8c,
				0x09, 0x66, 0x40, 0x64, 0x72, 0x65, 0xda, 0x1e
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8
			}, {
				0x5a, 0x4d, 0x40, 0x4d, 0x89, 0x17, 0xe3, 0x53,
				0xe9, 0x2a, 0x21, 0x07, 0x2c, 0x3b, 0x23, 0x05
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc
			}, {
				0x02, 0xbc, 0x96, 0x84, 0x6b, 0x3f, 0xdc, 0x71,
				0x64, 0x3f, 0x38, 0x4c, 0xd3, 0xcc, 0x3e, 0xaf
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe
			}, {
				0x9b, 0xa4, 0xa9, 0x14, 0x3f, 0x4e, 0x5d, 0x40,
				0x48, 0x52, 0x1c, 0x4f, 0x88, 0x77, 0xd8, 0x8e
			} },
		{{
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
				0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
			}, {
				0xa1, 0xf6, 0x25, 0x8c, 0x87, 0x7d, 0x5f, 0xcd,
				0x89, 0x64, 0x48, 0x45, 0x38, 0xbf, 0xc9, 0x2c
			} }
	};
	unsigned int i;

	TC_PRINT("AES128 test #4 (NIST variable-key and fixed-text):\n");

	for (i = 0; i < NUM_OF_FIXED_KEYS; ++i) {
		result = var_key_test(i, kat_tbl[i].in, kat_tbl[i].out);
		if (result == TC_FAIL) {
			break;
		}

	}

	TC_END_RESULT(result);

	return result;
}

/*
 * Main task to test AES
 */
int main(void)
{
	int result = TC_PASS;

	TC_START("Performing AES128 tests:");

	result = test_1();
	if (result == TC_FAIL) { /* terminate test */
		TC_ERROR("AES128 test #1 (NIST key schedule test) failed.\n");
		goto exitTest;
	}
	result = test_2();
	if (result == TC_FAIL) { /* terminate test */
		TC_ERROR("AES128 test #2 (NIST encryption test) failed.\n");
		goto exitTest;
	}
	result = test_3();
	if (result == TC_FAIL) { /* terminate test */
		TC_ERROR("AES128 test #3 (NIST fixed-key and variable-text) "
			 "failed.\n");
		goto exitTest;
	}
	result = test_4();
	if (result == TC_FAIL) { /* terminate test */
		TC_ERROR("AES128 test #4 (NIST variable-key and fixed-text) "
			 "failed.\n");
		goto exitTest;
	}

	TC_PRINT("All AES128 tests succeeded!\n");

 exitTest:
	TC_END_RESULT(result);
	TC_END_REPORT(result);

	return result;
}
