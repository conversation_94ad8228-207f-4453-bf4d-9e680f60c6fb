<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script></head><body class="PopupSearchResultsPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=SRStatus id=Loading>Loading...</div><table border=0 cellspacing=0 cellpadding=0><div class=SRResult id=SR_PERL><div class=IEntry><a href="javascript:searchResults.Toggle('SR_PERL')" class=ISymbol>PERL</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#PERL" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#PERL" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_PORT_undOBJS><div class=IEntry><a href="javascript:searchResults.Toggle('SR_PORT_undOBJS')" class=ISymbol>PORT_OBJS</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#PORT_OBJS" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#PORT_OBJS" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_port_undpostbuild><div class=IEntry><a href="javascript:searchResults.Toggle('SR_port_undpostbuild')" class=ISymbol>port_postbuild</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postbuild" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postbuild" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_port_undpostload><div class=IEntry><a href="javascript:searchResults.Toggle('SR_port_undpostload')" class=ISymbol>port_postload</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postload" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postload" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_port_undpostrun><div class=IEntry><a href="javascript:searchResults.Toggle('SR_port_undpostrun')" class=ISymbol>port_postrun</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_postrun" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_postrun" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_port_undprebuild><div class=IEntry><a href="javascript:searchResults.Toggle('SR_port_undprebuild')" class=ISymbol>port_prebuild</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_prebuild" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_prebuild" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_port_undpreload><div class=IEntry><a href="javascript:searchResults.Toggle('SR_port_undpreload')" class=ISymbol>port_preload</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_preload" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_preload" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_port_undprerun><div class=IEntry><a href="javascript:searchResults.Toggle('SR_port_undprerun')" class=ISymbol>port_prerun</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#port_prerun" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#port_prerun" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_PORT_undSRCS><div class=IEntry><a href="../files/linux/core_portme-mak.html#PORT_SRCS" target=_parent class=ISymbol>PORT_SRCS</a></div></div><div class=SRResult id=SR_portable_undfini><div class=IEntry><a href="../files/linux/core_portme-c.html#portable_fini" target=_parent class=ISymbol>portable_fini</a></div></div><div class=SRResult id=SR_portable_undfree><div class=IEntry><a href="../files/linux/core_portme-c.html#portable_free" target=_parent class=ISymbol>portable_free</a></div></div><div class=SRResult id=SR_portable_undinit><div class=IEntry><a href="../files/linux/core_portme-c.html#portable_init" target=_parent class=ISymbol>portable_init</a></div></div><div class=SRResult id=SR_portable_undmalloc><div class=IEntry><a href="../files/linux/core_portme-c.html#portable_malloc" target=_parent class=ISymbol>portable_malloc</a></div></div></table><div class=SRStatus id=Searching>Searching...</div><div class=SRStatus id=NoMatches>No Matches</div><script type="text/javascript"><!--
document.getElementById("Loading").style.display="none";
document.getElementById("NoMatches").style.display="none";
var searchResults = new SearchResults("searchResults", "HTML");
searchResults.Search();
--></script></div><script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>