<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>/cygdrive/d/dev/code/coremark/core_util.c - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CFile"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="core_util.c"></a>core_util.c</h1><div class=CBody><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#core_util.c" >core_util.c</a></td><td class=SDescription></td></tr><tr class="SGroup"><td class=SEntry><a href="#Functions" >Functions</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#get_seed" >get_seed</a></td><td class=SDescription>Get a values that cannot be determined at compile time.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#crc*" >crc*</a></td><td class=SDescription>Service functions to calculate 16b CRC code.</td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="Functions"></a>Functions</h3></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="get_seed"></a>get_seed</h3><div class=CBody><p>Get a values that cannot be determined at compile time.</p><h4 class=CHeading>Since different embedded systems and compilers are used, 3 different methods are provided</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>1</td><td class=CDLDescription>Using a volatile variable.&nbsp; This method is only valid if the compiler is forced to generate code that reads the value of a volatile variable from memory at run time.&nbsp; Please note, if using this method, you would need to modify core_portme.c to generate training profile.</td></tr><tr><td class=CDLEntry>2</td><td class=CDLDescription>Command line arguments.&nbsp; This is the preferred method if command line arguments are supported.</td></tr><tr><td class=CDLEntry>3</td><td class=CDLDescription>System function.&nbsp; If none of the first 2 methods is available on the platform, a system function which is not a stub can be used.</td></tr></table><p>e.g. read the value on GPIO pins connected to switches, or invoke special simulator functions.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="crc*"></a>crc*</h3><div class=CBody><p>Service functions to calculate 16b CRC code.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile id=MSelected>core_util.c</div></div><div class=MEntry><div class=MFile><a href="coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="../index/BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>