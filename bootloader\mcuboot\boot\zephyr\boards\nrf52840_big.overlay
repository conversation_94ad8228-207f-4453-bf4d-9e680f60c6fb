/*
 * Copyright (c) 2020 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: Apache-2.0
 */

/delete-node/ &boot_partition;
/delete-node/ &slot0_partition;
/delete-node/ &slot1_partition;

&flash0 {
	partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;

			boot_partition: partition@0 {
				label = "mcuboot";
				reg = <0x000000000 0x00010000>;
			};
			slot0_partition: partition@10000 {
				label = "image-0";
				reg = <0x000010000 0x000074000>;
			};
			slot1_partition: partition@75000 {
				label = "image-1";
				reg = <0x00084000 0x000074000>;
			};
	};
};

&zephyr_udc0 {
	cdc_acm_uart0 {
		compatible = "zephyr,cdc-acm-uart";
	};
};
