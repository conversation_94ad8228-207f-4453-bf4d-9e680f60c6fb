#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
# 
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

pkg.name: boot/bootutil
pkg.description: The bootutil library performs most of the functions of a boot loader.
pkg.author: "Apache Mynewt <<EMAIL>>"
pkg.homepage: "http://mynewt.apache.org/"
pkg.keywords:
    - boot
    - bootloader

pkg.apis:
    - bootloader

pkg.cflags:
    - "-DMCUBOOT_MYNEWT=1"

pkg.cflags.BOOTUTIL_USE_MBED_TLS:
    - '-DMBEDTLS_USER_CONFIG_FILE="mbedtls/config_mynewt.h"'

pkg.deps:
    - "@mcuboot/boot/mynewt/mcuboot_config"
    - "@apache-mynewt-core/hw/hal"
    - "@apache-mynewt-core/kernel/os"
    - "@apache-mynewt-core/sys/defs"
    - "@mcuboot/boot/mynewt/flash_map_backend"

pkg.ign_files.BOOTUTIL_SINGLE_APPLICATION_SLOT:
    - "loader.c"
    - "swap_scratch.c"

pkg.deps.BOOTUTIL_USE_MBED_TLS:
    - "@apache-mynewt-core/crypto/mbedtls"

pkg.deps.BOOTUTIL_USE_TINYCRYPT:
    - "@mcuboot/ext/tinycrypt/lib"
    - "@mcuboot/ext/mbedtls-asn1"

pkg.deps.BOOTUTIL_SIGN_ED25519:
    - "@mcuboot/ext/tinycrypt/lib"
    - "@mcuboot/ext/tinycrypt-sha512/lib"
    - "@mcuboot/ext/mbedtls-asn1"
    - "@mcuboot/ext/fiat"

pkg.deps.BOOTUTIL_ENCRYPT_X25519:
    - "@mcuboot/ext/tinycrypt/lib"
    - "@mcuboot/ext/tinycrypt-sha512/lib"
    - "@mcuboot/ext/mbedtls-asn1"
    - "@mcuboot/ext/fiat"
