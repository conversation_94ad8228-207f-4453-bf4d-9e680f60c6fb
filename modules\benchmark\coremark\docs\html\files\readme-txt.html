<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CFile"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="CoreMark"></a>CoreMark</h1><div class=CBody><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#CoreMark" >CoreMark</a></td><td class=SDescription></td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Welcome" >Welcome</a></td><td class=SDescription>Copyright © 2009 EEMBC All rights reserved. </td></tr><tr class="SGeneric"><td class=SEntry><a href="#Building_and_running" >Building and running</a></td><td class=SDescription>Download the release files from the www.coremark.org. </td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Documentation" >Documentation</a></td><td class=SDescription>When you unpack the documentation (tar -vzxf coremark_&lt;version&gt;_docs.tgz) a docs folder will be created. </td></tr><tr class="SGeneric"><td class=SEntry><a href="#Submitting_results" >Submitting results</a></td><td class=SDescription>CoreMark results can be submitted on the web.</td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Run_rules" >Run rules</a></td><td class=SDescription>What is and is not allowed.</td></tr><tr class="SGeneric"><td class=SEntry><a href="#Reporting_rules" >Reporting rules</a></td><td class=SDescription>How to report results on a data sheet?</td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Log_File_Format" >Log File Format</a></td><td class=SDescription>The log files have the following format</td></tr><tr class="SGeneric"><td class=SEntry><a href="#Legal" >Legal</a></td><td class=SDescription>See LICENSE.txt or the word document file under docs/LICENSE.doc. </td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Credits" >Credits</a></td><td class=SDescription>Many thanks to all of the individuals who helped with the development or testing of CoreMark including (Sorted by company name)</td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Welcome"></a>Welcome</h3><div class=CBody><p>Copyright © 2009 EEMBC All rights reserved.&nbsp; CoreMark is a trademark of EEMBC and EEMBC is a registered trademark of the Embedded Microprocessor Benchmark Consortium.</p><p>CoreMark’s primary goals are simplicity and providing a method for testing only a processor’s core features.</p><p>For more information about EEMBC&rsquo;s comprehensive embedded benchmark suites, please see www.eembc.org.</p></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Building_and_running"></a>Building and running</h3><div class=CBody><p>Download the release files from the www.coremark.org.&nbsp; You can verify the download using the coremark_&lt;version&gt;.md5 file</p><blockquote><pre>md5sum -c coremark_&lt;version&gt;.md5</pre></blockquote><p>Unpack the distribution (tar -vzxf coremark_&lt;version&gt;.tgz &amp;&amp; tar -vzxf coremark_&lt;version&gt;_docs.tgz) then change to the coremark_&lt;version&gt; folder.</p><p>To build and run the benchmark, type</p><blockquote><pre>make</pre></blockquote><p>Full results are available in the files run1.log and run2.log.&nbsp; CoreMark result can be found in run1.log.</p><p>For self hosted Linux or Cygwin platforms, a simple make should work.</p><h4 class=CHeading>Cross Compile</h4><p>For cross compile platforms please adjust <a href="linux/core_portme-mak.html#core_portme.mak" class=LFile >core_portme.mak</a>, <a href="linux/core_portme-h.html#core_portme.h" class=LFile >core_portme.h</a> (and possibly <a href="linux/core_portme-c.html#core_portme.c" class=LFile >core_portme.c</a>) according to the specific platform used.&nbsp; When porting to a new platform, it is recommended to copy one of the default port folders (e.g. mkdir &lt;platform&gt; &amp;&amp; cp linux/* &lt;platform&gt;), adjust the porting files, and run</p><blockquote><pre>make PORT_DIR=&lt;platform&gt;</pre></blockquote><h4 class=CHeading>Systems without make</h4><p>The following files need to be compiled:</p><ul><li><a href="core_list_join-c.html#core_list_join.c" class=LFile >core_list_join.c</a></li><li><a href="core_main-c.html#core_main.c" class=LFile id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">core_main.c</a></li><li><a href="core_matrix-c.html#core_matrix.c" class=LFile >core_matrix.c</a></li><li><a href="core_state-c.html#core_state.c" class=LFile >core_state.c</a></li><li><a href="core_util-c.html#core_util.c" class=LFile >core_util.c</a></li><li>&lt;PORT_DIR&gt;/<a href="linux/core_portme-c.html#core_portme.c" class=LFile >core_portme.c</a></li></ul><p>For example</p><blockquote><pre>gcc -O2 -o coremark.exe core_list_join.c core_main.c core_matrix.c core_state.c core_util.c simple/core_portme.c -DPERFORMANCE_RUN=1 -DITERATIONS=1000
./coremark.exe &gt; run1.log</pre></blockquote><p>The above will compile the benchmark for a performance run and 1000 iterations.&nbsp; Output is redirected to run1.log.</p><h4 class=CHeading>Make targets</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>run</td><td class=CDLDescription>Default target, creates run1.log and run2.log.</td></tr><tr><td class=CDLEntry>run1.log</td><td class=CDLDescription>Run the benchmark with performance parameters, and output to run1.log</td></tr><tr><td class=CDLEntry>run2.log</td><td class=CDLDescription>Run the benchmark with validation parameters, and output to run2.log</td></tr><tr><td class=CDLEntry>run3.log</td><td class=CDLDescription>Run the benchmark with profile generation parameters, and output to run3.log</td></tr><tr><td class=CDLEntry>compile</td><td class=CDLDescription>compile the benchmark executable</td></tr><tr><td class=CDLEntry>link</td><td class=CDLDescription>link the benchmark executable</td></tr><tr><td class=CDLEntry>check</td><td class=CDLDescription>test MD5 of sources that may not be modified</td></tr><tr><td class=CDLEntry>clean</td><td class=CDLDescription>clean temporary files</td></tr></table><h4 class=CHeading>ITERATIONS</h4><p>By default, the benchmark will run between 10-100 seconds.&nbsp; To override, use ITERATIONS=N</p><blockquote><pre>make ITERATIONS=10</pre></blockquote><p>Will run the benchmark for 10 iterations.&nbsp; It is recommended to set a specific number of iterations in certain situations e.g.:</p><ul><li>Running with a simulator</li><li>Measuring power/energy</li><li>Timing cannot be restarted</li></ul><h4 class=CHeading>Minimum required run time</h4><p>Results are only valid for reporting if the benchmark ran for at least 10 secs!</p><h4 class=CHeading>XCFLAGS</h4><p>To add compiler flags from the command line, use XCFLAGS e.g.</p><blockquote><pre>make XCFLAGS=&quot;-g -DMULTITHREAD=4 -DUSE_FORK=1&quot;</pre></blockquote><ul><li>CORE_DEBUG</li></ul><p>Define to compile for a debug run if you get incorrect CRC.</p><blockquote><pre>make XCFLAGS=&quot;-DCORE_DEBUG=1&quot;</pre></blockquote><ul><li>Parallel Execution</li></ul><p>Use XCFLAGS=-DMULTITHREAD=N where N is number of threads to run in parallel.&nbsp; Several implementations are available to execute in multiple contexts, or you can implement your own in <a href="linux/core_portme-c.html#core_portme.c" class=LFile >core_portme.c</a>.</p><blockquote><pre>make XCFLAGS=&quot;-DMULTITHREAD=4 -DUSE_PTHREAD&quot;</pre></blockquote><p>Above will compile the benchmark for execution on 4 cores, using POSIX Threads API.</p><h4 class=CHeading>REBUILD</h4><p>To force rebuild, add the flag REBUILD to the command line</p><blockquote><pre>make REBUILD=1</pre></blockquote><p>Check core_portme.mak for more important options.</p><h4 class=CHeading>Run parameters for the benchmark executable</h4><p>Coremark executable takes several parameters as follows (if main accepts arguments).&nbsp; 1st - A seed value used for initialization of data.&nbsp; 2nd - A seed value used for initialization of data.&nbsp; 3rd - A seed value used for initialization of data.&nbsp; 4th - Number of iterations (0 for auto : default value) 5th - Reserved for internal use.&nbsp; 6th - Reserved for internal use.&nbsp; 7th - For malloc users only, ovreride the size of the input data buffer.</p><p>The run target from make will run coremark with 2 different data initialization seeds.</p><h4 class=CHeading>Alternative parameters</h4><p>If not using malloc or command line arguments are not supported, the buffer size for the algorithms must be defined via the compiler define TOTAL_DATA_SIZE.&nbsp; TOTAL_DATA_SIZE must be set to 2000 bytes (default) for standard runs.&nbsp; The default for such a target when testing different configurations could be ...</p><blockquote><pre>make XCFLAGS=&quot;-DTOTAL_DATA_SIZE=6000 -DMAIN_HAS_NOARGC=1&quot;</pre></blockquote></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Documentation"></a>Documentation</h3><div class=CBody><p>When you unpack the documentation (tar -vzxf coremark_&lt;version&gt;_docs.tgz) a docs folder will be created.&nbsp; Check the file docs/html/index.html and the website <a href="http://www.coremark.org" class=LURL target=_top>http://www.coremark.org</a> for more info.</p></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Submitting_results"></a>Submitting results</h3><div class=CBody><p>CoreMark results can be submitted on the web.</p><p>Open a web browser and go to <a href="http://www.coremark.org/benchmark/index.php?pg=benchmark" class=LURL target=_top>http://www.coremark.org<span class=HB>- </span>/benchmark<span class=HB>- </span>/index.php?pg=benchmark</a> Select the link to add a new score and follow the instructions.</p></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Run_rules"></a>Run rules</h3><div class=CBody><p>What is and is not allowed.</p><h4 class=CHeading>Required</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>1</td><td class=CDLDescription>The benchmark needs to run for at least 10 seconds.</td></tr><tr><td class=CDLEntry>2</td><td class=CDLDescription>All validation must succeed for seeds 0,0,0x66 and 0x3415,0x3415,0x66, buffer size of 2000 bytes total.</td></tr></table><ul><li>If not using command line arguments to main:</li></ul><blockquote><pre>make XCFLAGS=&quot;-DPERFORMANCE_RUN=1&quot; REBUILD=1 run1.log
make XCFLAGS=&quot;-DVALIDATION_RUN=1&quot; REBUILD=1 run2.log</pre></blockquote><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>3</td><td class=CDLDescription>If using profile guided optimization, profile must be generated using seeds of 8,8,8, and buffer size of 1200 bytes total.</td></tr></table><blockquote><pre>make XCFLAGS=&quot;-DTOTAL_DATA_SIZE=1200 -DPROFILE_RUN=1&quot; REBUILD=1 run3.log</pre></blockquote><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>4</td><td class=CDLDescription>All source files must be compiled with the same flags.</td></tr><tr><td class=CDLEntry>5</td><td class=CDLDescription>All data type sizes must match size in bits such that:</td></tr></table><ul><li>ee_u8 is an 8 bits datatype.</li><li>ee_s16 is an 16 bits datatype.</li><li>ee_u16 is an 16 bits datatype.</li><li>ee_s32 is an 32 bits datatype.</li><li>ee_u32 is an 32 bits datatype.</li></ul><h4 class=CHeading>Allowed</h4><ul><li>Changing number of iterations</li><li>Changing toolchain and build/load/run options</li><li>Changing method of acquiring a data memory block</li><li>Changing the method of acquiring seed values</li><li>Changing implementation in core_portme.c</li><li>Changing configuration values in core_portme.h</li><li>Changing core_portme.mak</li></ul><h4 class=CHeading>Not allowed</h4><ul><li>Changing of source file other then core_portme* (use make check to validate)</li></ul></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Reporting_rules"></a>Reporting rules</h3><div class=CBody><p>How to report results on a data sheet?</p><p>CoreMark 1.0 : N / C [/ P] [/ M]</p><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>N</td><td class=CDLDescription>Number of iterations per second with seeds 0,0,0x66,size=2000)</td></tr><tr><td class=CDLEntry>C</td><td class=CDLDescription>Compiler version and flags</td></tr><tr><td class=CDLEntry>P</td><td class=CDLDescription>Parameters such as data and code allocation specifics</td></tr></table><ul><li>This parameter <b>may</b> be omitted if all data was allocated on the heap in RAM.</li><li>This parameter <b>may not</b> be omitted when reporting CoreMark/MHz</li></ul><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>M</td><td class=CDLDescription>Type of parallel execution (if used) and number of contexts This parameter may be omitted if parallel execution was not used.</td></tr></table><p>e.g.</p><blockquote><pre>CoreMark 1.0 : 128 / GCC 4.1.2 -O2 -fprofile-use / Heap in TCRAM / FORK:2</pre></blockquote><p>or</p><blockquote><pre>CoreMark 1.0 : 1400 / GCC 3.4 -O4</pre></blockquote><h4 class=CHeading>If reporting scaling results, the results must be reported as follows</h4><p>CoreMark/MHz 1.0 : N / C / P [/ M]</p><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>P</td><td class=CDLDescription>When reporting scaling results, memory parameter must also indicate memory frequency:core frequency ratio.</td></tr></table><ul><li>If the core has cache and cache frequency to core frequency ratio is configurable, that must also be included.</li></ul><p>e.g.</p><blockquote><pre>CoreMark/MHz 1.0 : 1.47 / GCC 4.1.2 -O2 / DDR3(Heap) 30:1 Memory 1:1 Cache</pre></blockquote></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Log_File_Format"></a>Log File Format</h3><div class=CBody><p>The log files have the following format</p><blockquote><pre>2K performance run parameters for coremark. (Run type)
CoreMark Size       : 666                   (Buffer size)
Total ticks         : 25875                 (platform dependent value)
Total time (secs)   : 25.875000             (actual time in seconds)
Iterations/Sec      : 3864.734300           (Performance value to report)
Iterations          : 100000                (number of iterations used)
Compiler version    : GCC3.4.4              (Compiler and version)
Compiler flags      : -O2                   (Compiler and linker flags)
Memory location     : Code in flash, data in on chip RAM
seedcrc             : 0xe9f5                (identifier for the input seeds)
[0]crclist          : 0xe714                (validation for list part)
[0]crcmatrix        : 0x1fd7                (validation for matrix part)
[0]crcstate         : 0x8e3a                (validation for state part)
[0]crcfinal         : 0x33ff                (iteration dependent output)
Correct operation validated. See README.md for run and reporting rules.  (*Only when run is successful*)
CoreMark 1.0 : 6508.490622 / GCC3.4.4 -O2 / Heap                          (*Only on a successful performance run*)</pre></blockquote></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Legal"></a>Legal</h3><div class=CBody><p>See LICENSE.txt or the word document file under docs/LICENSE.doc.&nbsp; For more information on your legal rights to use this benchmark, please see <a href="http://www.coremark.org/download/register.php?pg=register" class=LURL target=_top>http://www.coremark.org<span class=HB>- </span>/download<span class=HB>- </span>/register.php?pg=register</a></p></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Credits"></a>Credits</h3><div class=CBody><p>Many thanks to all of the individuals who helped with the development or testing of CoreMark including (Sorted by company name)</p><ul><li>Alan Anderson, ADI</li><li>Adhikary Rajiv, ADI</li><li>Elena Stohr, ARM</li><li>Ian Rickards, ARM</li><li>Andrew Pickard, ARM</li><li>Trent Parker, CAVIUM</li><li>Shay Gal-On, EEMBC</li><li>Markus Levy, EEMBC</li><li>Ron Olson, IBM</li><li>Eyal Barzilay, MIPS</li><li>Jens Eltze, NEC</li><li>Hirohiko Ono, NEC</li><li>Ulrich Drees, NEC</li><li>Frank Roscheda, NEC</li><li>Rob Cosaro, NXP</li><li>Shumpei Kawasaki, RENESAS</li></ul></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile id=MSelected>CoreMark</div></div><div class=MEntry><div class=MFile><a href="release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="../index/Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="../index/BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFile>This file contains the framework to acquire a block of memory, seed initial parameters, tun t he benchmark and report the results.</div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>
