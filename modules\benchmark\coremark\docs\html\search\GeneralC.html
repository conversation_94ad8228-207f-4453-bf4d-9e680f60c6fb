<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script></head><body class="PopupSearchResultsPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=SRStatus id=Loading>Loading...</div><table border=0 cellspacing=0 cellpadding=0><div class=SRResult id=SR_CC><div class=IEntry><a href="../files/linux/core_portme-mak.html#CC" target=_parent class=ISymbol>CC</a></div></div><div class=SRResult id=SR_CFLAGS><div class=IEntry><a href="javascript:searchResults.Toggle('SR_CFLAGS')" class=ISymbol>CFLAGS</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#CFLAGS" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#CFLAGS" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_cmp_undcomplex><div class=IEntry><a href="../files/core_list_join-c.html#cmp_complex" target=_parent class=ISymbol>cmp_complex</a></div></div><div class=SRResult id=SR_cmp_undidx><div class=IEntry><a href="../files/core_list_join-c.html#cmp_idx" target=_parent class=ISymbol>cmp_idx</a></div></div><div class=SRResult id=SR_Configuration><div class=IEntry><a href="javascript:searchResults.Toggle('SR_Configuration')" class=ISymbol>Configuration</a><div class=ISubIndex><a href="../files/coremark-h.html#Configuration" target=_parent class=IFile>coremark.h</a><a href="../files/linux/core_portme-h.html#Configuration" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.h</a></div></div></div><div class=SRResult id=SR_core_undbench_undmatrix><div class=IEntry><a href="../files/core_matrix-c.html#core_bench_matrix" target=_parent class=ISymbol>core_bench_matrix</a></div></div><div class=SRResult id=SR_core_undbench_undstate><div class=IEntry><a href="../files/core_state-c.html#core_bench_state" target=_parent class=ISymbol>core_bench_state</a></div></div><div class=SRResult id=SR_core_undinit_undstate><div class=IEntry><a href="../files/core_state-c.html#core_init_state" target=_parent class=ISymbol>core_init_state</a></div></div><div class=SRResult id=SR_core_undlist_undfind><div class=IEntry><a href="../files/core_list_join-c.html#core_list_find" target=_parent class=ISymbol>core_list_find</a></div></div><div class=SRResult id=SR_core_undlist_undinit><div class=IEntry><a href="../files/core_list_join-c.html#core_list_init" target=_parent class=ISymbol>core_list_init</a></div></div><div class=SRResult id=SR_core_undlist_undinsert><div class=IEntry><a href="../files/core_list_join-c.html#core_list_insert" target=_parent class=ISymbol>core_list_insert</a></div></div><div class=SRResult id=SR_core_undlist_undjoin_perc><div class=IEntry><a href="../files/core_list_join-c.html#core_list_join.c" target=_parent class=ISymbol>core_list_join.c</a></div></div><div class=SRResult id=SR_core_undlist_undmergesort><div class=IEntry><a href="../files/core_list_join-c.html#core_list_mergesort" target=_parent class=ISymbol>core_list_mergesort</a></div></div><div class=SRResult id=SR_core_undlist_undremove><div class=IEntry><a href="../files/core_list_join-c.html#core_list_remove" target=_parent class=ISymbol>core_list_remove</a></div></div><div class=SRResult id=SR_core_undlist_undreverse><div class=IEntry><a href="../files/core_list_join-c.html#core_list_reverse" target=_parent class=ISymbol>core_list_reverse</a></div></div><div class=SRResult id=SR_core_undlist_undundo_undremove><div class=IEntry><a href="../files/core_list_join-c.html#core_list_undo_remove" target=_parent class=ISymbol>core_list_undo_remove</a></div></div><div class=SRResult id=SR_core_undmain_perc><div class=IEntry><a href="../files/core_main-c.html#core_main.c" target=_parent class=ISymbol>core_main.c</a></div></div><div class=SRResult id=SR_core_undmatrix_perc><div class=IEntry><a href="../files/core_matrix-c.html#core_matrix.c" target=_parent class=ISymbol>core_matrix.c</a></div></div><div class=SRResult id=SR_core_undportme_perc><div class=IEntry><a href="../files/linux/core_portme-c.html#core_portme.c" target=_parent class=ISymbol>core_portme.c</a></div></div><div class=SRResult id=SR_core_undportme_perh><div class=IEntry><a href="../files/linux/core_portme-h.html#core_portme.h" target=_parent class=ISymbol>core_portme.h</a></div></div><div class=SRResult id=SR_core_undportme_permak><div class=IEntry><a href="javascript:searchResults.Toggle('SR_core_undportme_permak')" class=ISymbol>core_portme.mak</a><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#core_portme.mak" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#core_portme.mak" target=_parent class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></div></div><div class=SRResult id=SR_core_undstart_undparallel><div class=IEntry><a href="../files/linux/core_portme-c.html#core_start_parallel" target=_parent class=ISymbol>core_start_parallel</a></div></div><div class=SRResult id=SR_core_undstate_perc><div class=IEntry><a href="../files/core_state-c.html#core_state.c" target=_parent class=ISymbol>core_state.c</a></div></div><div class=SRResult id=SR_core_undstate_undtransition><div class=IEntry><a href="../files/core_state-c.html#core_state_transition" target=_parent class=ISymbol>core_state_transition</a></div></div><div class=SRResult id=SR_core_undstop_undparallel><div class=IEntry><a href="../files/linux/core_portme-c.html#core_stop_parallel" target=_parent class=ISymbol>core_stop_parallel</a></div></div><div class=SRResult id=SR_CORE_undTICKS><div class=IEntry><a href="../files/linux/core_portme-h.html#CORE_TICKS" target=_parent class=ISymbol>CORE_TICKS</a></div></div><div class=SRResult id=SR_core_undutil_perc><div class=IEntry><a href="../files/core_util-c.html#core_util.c" target=_parent class=ISymbol>core_util.c</a></div></div><div class=SRResult id=SR_CoreMark><div class=IEntry><a href="../files/readme-txt.html#CoreMark" target=_parent class=ISymbol>CoreMark</a></div></div><div class=SRResult id=SR_coremark_perh><div class=IEntry><a href="../files/coremark-h.html#coremark.h" target=_parent class=ISymbol>coremark.h</a></div></div><div class=SRResult id=SR_crc_ast><div class=IEntry><a href="../files/core_util-c.html#crc*" target=_parent class=ISymbol>crc*</a></div></div><div class=SRResult id=SR_Credits><div class=IEntry><a href="../files/readme-txt.html#Credits" target=_parent class=ISymbol>Credits</a></div></div></table><div class=SRStatus id=Searching>Searching...</div><div class=SRStatus id=NoMatches>No Matches</div><script type="text/javascript"><!--
document.getElementById("Loading").style.display="none";
document.getElementById("NoMatches").style.display="none";
var searchResults = new SearchResults("searchResults", "HTML");
searchResults.Search();
--></script></div><script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>