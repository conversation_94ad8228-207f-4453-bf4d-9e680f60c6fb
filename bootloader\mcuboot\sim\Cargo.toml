[package]
name = "bootsim"
version = "0.1.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"

[features]
default = []

sig-rsa = ["mcuboot-sys/sig-rsa"]
sig-rsa3072 = ["mcuboot-sys/sig-rsa3072"]
sig-ecdsa = ["mcuboot-sys/sig-ecdsa"]
sig-ecdsa-mbedtls = ["mcuboot-sys/sig-ecdsa-mbedtls"]
sig-ecdsa-psa = ["mcuboot-sys/sig-ecdsa-psa", "mcuboot-sys/psa-crypto-api"]
sig-p384 = ["mcuboot-sys/sig-p384"]
sig-ed25519 = ["mcuboot-sys/sig-ed25519"]
overwrite-only = ["mcuboot-sys/overwrite-only"]
swap-move = ["mcuboot-sys/swap-move"]
validate-primary-slot = ["mcuboot-sys/validate-primary-slot"]
enc-rsa = ["mcuboot-sys/enc-rsa"]
enc-aes256-rsa = ["mcuboot-sys/enc-aes256-rsa"]
enc-kw = ["mcuboot-sys/enc-kw"]
enc-aes256-kw = ["mcuboot-sys/enc-aes256-kw"]
enc-ec256 = ["mcuboot-sys/enc-ec256"]
enc-ec256-mbedtls = ["mcuboot-sys/enc-ec256-mbedtls"]
enc-aes256-ec256 = ["mcuboot-sys/enc-aes256-ec256"]
enc-x25519 = ["mcuboot-sys/enc-x25519"]
enc-aes256-x25519 = ["mcuboot-sys/enc-aes256-x25519"]
bootstrap = ["mcuboot-sys/bootstrap"]
multiimage = ["mcuboot-sys/multiimage"]
ram-load = ["mcuboot-sys/ram-load"]
direct-xip = ["mcuboot-sys/direct-xip"]
downgrade-prevention = ["mcuboot-sys/downgrade-prevention"]
max-align-32 = ["mcuboot-sys/max-align-32"]
hw-rollback-protection = ["mcuboot-sys/hw-rollback-protection"]

[dependencies]
byteorder = "1.4"
libc = "0.2"
rand = { version = "0.8", features = ["small_rng"] }
docopt = "1.1.0"
serde = "1.0"
serde_derive = "1.0"
log = "0.4"
env_logger = "0.9"
simflash = { path = "simflash" }
mcuboot-sys = { path = "mcuboot-sys" }
ring = "0.16.11"
untrusted = "0.9"
pem = "1.0"
cipher = "0.3"
aes = { version = "0.7.4", features = ["ctr"] }
base64 = "0.13.0"
typenum = "1.13.0"
