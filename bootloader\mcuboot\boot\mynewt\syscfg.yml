# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

# Package: boot/mynewt

syscfg.defs:
    BOOT_LOADER:
        description: 'Set to indicate that this app is a bootloader.'
        value: 1
    BOOT_SERIAL:
        description: 'Support image upgrade over serial within bootloader'
        value: 0
    BOOT_CUSTOM_START:
        description: 'Override hal_system_start with a custom start routine'
        value:
    BOOT_PREBOOT:
        description: 'Call boot_preboot() function before booting application'
        value:
    BOOT_MYNEWT_SYSINIT:
        description: >
            When not 0 performs device initialization and calls newt
            generated sysinit() function.
            Note: this functionality is implicitly turned on when one of the
            following settings are not 0:
            MCUBOOT_SERIAL, MCUBOOT_HAVE_LOGGING, CRYPTO, HASH
        value: 0

syscfg.vals:
    SYSINIT_CONSTRAIN_INIT: 0
    OS_SCHEDULING: 0
    MSYS_1_BLOCK_COUNT: 0
    CONSOLE_COMPAT: 1
