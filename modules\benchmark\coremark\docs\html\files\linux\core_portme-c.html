<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>core_portme.c - CoreMark</title><link rel="stylesheet" type="text/css" href="../../styles/main.css"><script language=JavaScript src="../../javascript/main.js"></script><script language=JavaScript src="../../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CFile"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="core_portme.c"></a>core_portme.c</h1><div class=CBody><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#core_portme.c" >core_portme.c</a></td><td class=SDescription></td></tr><tr class="SFunction SMarked"><td class=SEntry><a href="#portable_malloc" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">portable_malloc</a></td><td class=SDescription>Provide malloc() functionality in a platform specific way.</td></tr><tr class="SFunction"><td class=SEntry><a href="#portable_free" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')">portable_free</a></td><td class=SDescription>Provide free() functionality in a platform specific way.</td></tr><tr class="SGroup"><td class=SEntry><a href="#TIMER_RES_DIVIDER" >TIMER_RES_DIVIDER</a></td><td class=SDescription>Divider to trade off timer resolution and total time that can be measured.</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#start_time" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')">start_time</a></td><td class=SDescription>This function will be called right before starting the timed portion of the benchmark.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#stop_time" id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')">stop_time</a></td><td class=SDescription>This function will be called right after ending the timed portion of the benchmark.</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#get_time" id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')">get_time</a></td><td class=SDescription>Return an abstract &ldquo;ticks&rdquo; number that signifies time on the system.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#time_in_secs" id=link6 onMouseOver="ShowTip(event, 'tt6', 'link6')" onMouseOut="HideTip('tt6')">time_in_secs</a></td><td class=SDescription>Convert the value returned by get_time to seconds.</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#portable_init" id=link7 onMouseOver="ShowTip(event, 'tt7', 'link7')" onMouseOut="HideTip('tt7')">portable_init</a></td><td class=SDescription>Target specific initialization code Test for some common mistakes.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#portable_fini" id=link8 onMouseOver="ShowTip(event, 'tt8', 'link8')" onMouseOut="HideTip('tt8')">portable_fini</a></td><td class=SDescription>Target specific final code</td></tr><tr class="SFunction SIndent1 SMarked"><td class=SEntry><a href="#core_start_parallel" >core_start_parallel</a></td><td class=SDescription>Start benchmarking in a parallel context.</td></tr><tr class="SFunction SIndent1"><td class=SEntry><a href="#core_stop_parallel" >core_stop_parallel</a></td><td class=SDescription>Stop a parallel context execution of coremark, and gather the results.</td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="portable_malloc"></a>portable_malloc</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void *portable_malloc(</td><td class=PType nowrap>size_t&nbsp;</td><td class=PParameter nowrap>size</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Provide malloc() functionality in a platform specific way.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="portable_free"></a>portable_free</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_free(</td><td class=PType nowrap>void&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Provide free() functionality in a platform specific way.</p></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="TIMER_RES_DIVIDER"></a>TIMER_RES_DIVIDER</h3><div class=CBody><p>Divider to trade off timer resolution and total time that can be measured.</p><p>Use lower values to increase resolution, but make sure that overflow does not occur.&nbsp; If there are issues with the return value overflowing, increase this value.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="start_time"></a>start_time</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void start_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>This function will be called right before starting the timed portion of the benchmark.</p><p>Implementation may be capturing a system timer (as implemented in the example code) or zeroing some system parameters - e.g. setting the cpu clocks cycles to 0.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="stop_time"></a>stop_time</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void stop_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>This function will be called right after ending the timed portion of the benchmark.</p><p>Implementation may be capturing a system timer (as implemented in the example code) or other system parameters - e.g. reading the current value of cpu cycles counter.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="get_time"></a>get_time</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>CORE_TICKS get_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Return an abstract &ldquo;ticks&rdquo; number that signifies time on the system.</p><p>Actual value returned may be cpu cycles, milliseconds or any other value, as long as it can be converted to seconds by <a href="#time_in_secs" class=LFunction id=link9 onMouseOver="ShowTip(event, 'tt6', 'link9')" onMouseOut="HideTip('tt6')">time_in_secs</a>.&nbsp; This methodology is taken to accomodate any hardware or simulated platform.&nbsp; The sample implementation returns millisecs by default, and the resolution is controlled by <a href="#TIMER_RES_DIVIDER" class=LGroup id=link10 onMouseOver="ShowTip(event, 'tt9', 'link10')" onMouseOut="HideTip('tt9')">TIMER_RES_DIVIDER</a></p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="time_in_secs"></a>time_in_secs</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>secs_ret time_in_secs(</td><td class=PType nowrap>CORE_TICKS&nbsp;</td><td class=PParameter nowrap>ticks</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Convert the value returned by get_time to seconds.</p><p>The <a href="../coremark-h.html#secs_ret" class=LType id=link11 onMouseOver="ShowTip(event, 'tt10', 'link11')" onMouseOut="HideTip('tt10')">secs_ret</a> type is used to accomodate systems with no support for floating point.&nbsp; Default implementation implemented by the EE_TICKS_PER_SEC macro above.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="portable_init"></a>portable_init</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_init(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>int&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argc,</td></tr><tr><td></td><td class=PType nowrap>char&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argv[]</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Target specific initialization code Test for some common mistakes.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="portable_fini"></a>portable_fini</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_fini(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote><p>Target specific final code</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_start_parallel"></a>core_start_parallel</h3><div class=CBody><p>Start benchmarking in a parallel context.</p><p>Three implementations are provided, one using pthreads, one using fork and shared mem, and one using fork and sockets.&nbsp; Other implementations using MCAPI or other standards can easily be devised.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="core_stop_parallel"></a>core_stop_parallel</h3><div class=CBody><p>Stop a parallel context execution of coremark, and gather the results.</p><p>Three implementations are provided, one using pthreads, one using fork and shared mem, and one using fork and sockets.&nbsp; Other implementations using MCAPI or other standards can easily be devised.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="../readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="../release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="../core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="../core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="../core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="../coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile id=MSelected>PORT_DIR/<span class=HB> </span>core_portme.c</div></div><div class=MEntry><div class=MFile><a href="core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="../../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void *portable_malloc(</td><td class=PType nowrap>size_t&nbsp;</td><td class=PParameter nowrap>size</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Provide malloc() functionality in a platform specific way.</div></div><div class=CToolTip id="tt2"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_free(</td><td class=PType nowrap>void&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Provide free() functionality in a platform specific way.</div></div><div class=CToolTip id="tt3"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void start_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>This function will be called right before starting the timed portion of the benchmark.</div></div><div class=CToolTip id="tt4"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void stop_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>This function will be called right after ending the timed portion of the benchmark.</div></div><div class=CToolTip id="tt5"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>CORE_TICKS get_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Return an abstract &ldquo;ticks&rdquo; number that signifies time on the system.</div></div><div class=CToolTip id="tt6"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>secs_ret time_in_secs(</td><td class=PType nowrap>CORE_TICKS&nbsp;</td><td class=PParameter nowrap>ticks</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Convert the value returned by get_time to seconds.</div></div><div class=CToolTip id="tt7"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_init(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>int&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argc,</td></tr><tr><td></td><td class=PType nowrap>char&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argv[]</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Target specific initialization code Test for some common mistakes.</div></div><div class=CToolTip id="tt8"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_fini(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Target specific final code</div></div><div class=CToolTip id="tt9"><div class=CGroup>Divider to trade off timer resolution and total time that can be measured.</div></div><div class=CToolTip id="tt10"><div class=CType>For machines that have floating point support, get number of seconds as a double. </div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>