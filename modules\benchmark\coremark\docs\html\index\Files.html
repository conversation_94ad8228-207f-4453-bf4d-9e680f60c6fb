<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>File Index - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="IndexPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=IPageTitle>File Index</div><div class=INavigationBar>$#! &middot; 0-9 &middot; A &middot; B &middot; <a href="#C">C</a> &middot; D &middot; E &middot; F &middot; G &middot; H &middot; I &middot; J &middot; K &middot; L &middot; M &middot; N &middot; O &middot; P &middot; Q &middot; <a href="#R">R</a> &middot; S &middot; T &middot; U &middot; V &middot; W &middot; X &middot; Y &middot; Z</div><table border=0 cellspacing=0 cellpadding=0><tr><td class=IHeading id=IFirstHeading><a name="C"></a>C</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_join.c"  class=ISymbol>core_list_join.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_main-c.html#core_main.c" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')" class=ISymbol>core_main.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#core_matrix.c"  class=ISymbol>core_matrix.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#core_portme.c"  class=ISymbol>core_portme.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-h.html#core_portme.h"  class=ISymbol>core_portme.h</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>core_portme.mak</span><div class=ISubIndex><a href="../files/linux/core_portme-mak.html#core_portme.mak"  class=IFile>linux/<span class=HB> </span>core_portme.mak</a><a href="../files/PIC32/core_portme-mak.html#core_portme.mak"  class=IFile>PIC32/<span class=HB> </span>core_portme.mak</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_state-c.html#core_state.c"  class=ISymbol>core_state.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_util-c.html#core_util.c"  class=ISymbol>core_util.c</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/readme-txt.html#CoreMark"  class=ISymbol>CoreMark</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/coremark-h.html#coremark.h"  class=ISymbol>coremark.h</a></td></tr><tr><td class=IHeading><a name="R"></a>R</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/release_notes-txt.html#Release_Notes" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')" class=ISymbol>Release Notes</a></td></tr></table>
<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFile>This file contains the framework to acquire a block of memory, seed initial parameters, tun t he benchmark and report the results.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt2"><div class=CFile>Version: 1.01</div></div><!--END_ND_TOOLTIPS-->

</div><!--Index-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="../files/readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="../files/release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../files/core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="General.html">Everything</a></div></div><div class=MEntry><div class=MIndex id=MSelected>Files</div></div><div class=MEntry><div class=MIndex><a href="Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->


<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>