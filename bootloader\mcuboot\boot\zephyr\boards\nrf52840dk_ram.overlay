/*
 * Copyright (c) 2021 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: Apache-2.0
 */

/delete-node/ &slot1_partition;
/delete-node/ &slot0_partition;
/delete-node/ &boot_partition;

&flash0 {
	partitions {
		boot_partition: partition@0 {
			label = "mcuboot";
			reg = <0x000000000 0x00010000>;
		};
		slot0_partition: partition@10000 {
			label = "image-0";
			reg = <0x000010000 0x00000A000>;
		};
	};
};

/ {
	soc {
		flash_controller2: flash-controller@2 {
			compatible = "zephyr,sim-flash";
			reg = <0x00000000 DT_SIZE_K(40)>;

			#address-cells = <1>;
			#size-cells = <1>;
			erase-value = <0xff>;

			flash_sim0: flash_sim@0 {
				status = "okay";
				compatible = "soc-nv-flash";
				erase-block-size = <4096>;
				write-block-size = <1>;
				reg = <0x00000000 DT_SIZE_K(40)>;

				partitions {
					compatible = "fixed-partitions";
					#address-cells = <1>;
					#size-cells = <1>;

					slot1_partition: partition@0 {
						label = "image-1";
						reg = <0x00000000 0x00000A000>;
					};
				};
			};
		};
	};
};
