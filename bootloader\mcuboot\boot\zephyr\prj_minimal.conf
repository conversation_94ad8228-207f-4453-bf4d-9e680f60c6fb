#
# Copyright (c) 2021 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

CONFIG_MAIN_STACK_SIZE=10240
CONFIG_MBEDTLS_CFG_FILE="mcuboot-mbedtls-cfg.h"

CONFIG_FLASH=y
CONFIG_FPROTECT=y
CONFIG_PM=n

CONFIG_BOOT_SWAP_SAVE_ENCTLV=n
CONFIG_BOOT_ENCRYPT_IMAGE=n

CONFIG_BOOT_BOOTSTRAP=n
CONFIG_BOOT_UPGRADE_ONLY=n

### Minimal Configurations ###
CONFIG_BOOT_USE_MIN_PARTITION_SIZE=y
CONFIG_ASSERT=n
CONFIG_BOOT_BANNER=n
CONFIG_CLOCK_CONTROL=n
CONFIG_CONSOLE=n
CONFIG_CONSOLE_HANDLER=n
CONFIG_GPIO=n
CONFIG_KERNEL_MEM_POOL=n
CONFIG_LOG=n
CONFIG_MINIMAL_LIBC_CALLOC=n
CONFIG_MINIMAL_LIBC_MALLOC=n
CONFIG_MINIMAL_LIBC_REALLOCARRAY=n
CONFIG_NCS_SAMPLES_DEFAULTS=n
CONFIG_NO_RUNTIME_CHECKS=y
CONFIG_NRF_RTC_TIMER=n
CONFIG_PRINTK=n
CONFIG_SERIAL=n
CONFIG_SIZE_OPTIMIZATIONS=y
CONFIG_SYS_CLOCK_EXISTS=n
CONFIG_UART_CONSOLE=n
