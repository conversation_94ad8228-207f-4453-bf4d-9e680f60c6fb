name: "Close stale pull requests/issues"
on:
  schedule:
  - cron: "16 00 * * *"

jobs:
  stale:
    name: Find Stale issues and PRs
    runs-on: ubuntu-latest
    if: github.repository == 'mcu-tools/mcuboot'
    steps:
    - uses: actions/stale@v3
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-pr-message: 'This pull request has been marked as stale because it has been open (more than) 60 days with no activity. Remove the stale label or add a comment saying that you would like to have the label removed otherwise this pull request will automatically be closed in 14 days. Note, that you can always re-open a closed pull request at any time.'
        stale-issue-message: 'This issue has been marked as stale because it has been open (more than) 60 days with no activity. Remove the stale label or add a comment saying that you would like to have the label removed otherwise this issue will automatically be closed in 14 days. Note, that you can always re-open a closed issue at any time.'
        days-before-stale: 180
        days-before-close: 14
        stale-issue-label: 'stale'
        stale-pr-label: 'stale'
        exempt-issue-labels: 'someday'
        # exempt-pr-labels: 'Blocked,In progress'
        # exempt-issue-labels: 'In progress,Enhancement,Feature,Feature Request,RFC,Meta'
        operations-per-run: 400
