/* Autogenerated by imgtool.py, do not edit. */
static RSA3072_PUB_KEY: &[u8] = &[
    0x30, 0x82, 0x01, 0x8a, 0x02, 0x82, 0x01, 0x81,
    0x00, 0xb4, 0x2c, 0x0e, 0x98, 0x58, 0x10, 0xa4,
    0xa7, 0x58, 0x99, 0x7c, 0x01, 0xdd, 0x08, 0x2a,
    0x28, 0x34, 0x33, 0xf8, 0x96, 0x1a, 0x34, 0x20,
    0x5d, 0x45, 0xc8, 0x71, 0x26, 0x25, 0xe5, 0xd2,
    0x96, 0xea, 0x7b, 0xb1, 0x15, 0xaa, 0xa6, 0x8a,
    0x63, 0x22, 0x8b, 0x2d, 0x4e, 0x81, 0x73, 0xbf,
    0x6e, 0x15, 0x68, 0x8c, 0x1a, 0xf4, 0xef, 0x2a,
    0x8f, 0x8c, 0x22, 0x9e, 0x71, 0x57, 0x4b, 0xde,
    0x0f, 0x7e, 0x72, 0xd3, 0x7a, 0xb8, 0xa7, 0x1d,
    0x44, 0xad, 0x87, 0x00, 0x83, 0x5c, 0xfd, 0x73,
    0x05, 0x72, 0x46, 0x3f, 0x8b, 0xf9, 0x10, 0x00,
    0xd8, 0x6e, 0xcc, 0x85, 0xed, 0xf9, 0x49, 0xdb,
    0x78, 0x36, 0x80, 0x49, 0x38, 0x76, 0xdd, 0x5f,
    0x54, 0x04, 0xda, 0x8c, 0x34, 0xa7, 0x2b, 0x13,
    0x25, 0x6f, 0xd1, 0x15, 0x4f, 0xad, 0xc2, 0xe1,
    0xa5, 0xd2, 0x4e, 0x57, 0x0c, 0x7e, 0x9c, 0x9b,
    0xba, 0x4e, 0x68, 0xb2, 0xe0, 0x25, 0x02, 0xaa,
    0x00, 0xd3, 0xb4, 0xcc, 0x2f, 0x78, 0xe5, 0xbe,
    0x47, 0x67, 0x1f, 0xc8, 0x6e, 0x22, 0x6c, 0x5e,
    0x61, 0xb6, 0x9a, 0xcd, 0xe5, 0xa8, 0xba, 0x7a,
    0x80, 0x13, 0x1b, 0x17, 0x2e, 0x96, 0xed, 0xcf,
    0xb3, 0x9b, 0xe4, 0x1c, 0xe8, 0xad, 0xa7, 0xf6,
    0x3a, 0x51, 0x66, 0x5e, 0x99, 0x8e, 0x87, 0xee,
    0x60, 0x25, 0xf8, 0x8d, 0xbe, 0xce, 0xa4, 0xa8,
    0xca, 0x93, 0x6c, 0xd7, 0xbf, 0xd4, 0x73, 0x33,
    0x8d, 0x44, 0x85, 0xcc, 0x73, 0x30, 0x08, 0x9c,
    0x4d, 0xb2, 0xaa, 0x5a, 0x6c, 0x6f, 0x7b, 0xab,
    0xb7, 0xb3, 0x7c, 0xc3, 0xfb, 0xe7, 0xca, 0xc4,
    0xf8, 0x9a, 0x6f, 0xcb, 0xbb, 0x5b, 0x82, 0xe7,
    0x7a, 0xe8, 0x19, 0xfd, 0x2f, 0x11, 0x22, 0xfb,
    0x7f, 0x76, 0x8c, 0x6b, 0x94, 0xa4, 0x09, 0x4f,
    0xa5, 0x6a, 0x77, 0x51, 0xeb, 0xa7, 0x7e, 0xda,
    0x87, 0x06, 0xee, 0xdc, 0xbe, 0xd1, 0xea, 0x1a,
    0x40, 0x1d, 0x1b, 0xff, 0x1a, 0xb1, 0x51, 0x7c,
    0x12, 0xb0, 0xf3, 0xf6, 0x83, 0x01, 0x9c, 0xe7,
    0x0c, 0x99, 0xbf, 0xac, 0x68, 0x58, 0x72, 0xa4,
    0xb0, 0x59, 0x85, 0xee, 0x85, 0xac, 0x2a, 0x22,
    0xf4, 0xcf, 0x15, 0x08, 0x80, 0x1f, 0x0d, 0xd0,
    0x1e, 0xa0, 0xa0, 0x94, 0xc8, 0xf7, 0xfa, 0x65,
    0xdd, 0x52, 0xe8, 0x96, 0x37, 0x23, 0x30, 0x57,
    0x36, 0xe6, 0x9d, 0xf4, 0x0c, 0x4a, 0x05, 0x75,
    0x1f, 0xad, 0x01, 0xca, 0xb7, 0x6d, 0x8c, 0x43,
    0x74, 0x06, 0x0a, 0x81, 0xf3, 0x01, 0x62, 0xff,
    0xf7, 0xf5, 0x5f, 0xaf, 0xe7, 0x2b, 0x0e, 0xf8,
    0x81, 0xb5, 0x65, 0xdd, 0x01, 0xd9, 0x9f, 0x07,
    0x17, 0x8a, 0x18, 0xcf, 0x23, 0x6e, 0x88, 0x65,
    0x91, 0xb5, 0x7b, 0xd3, 0xb0, 0x2d, 0xaf, 0x93,
    0x66, 0x63, 0x74, 0xac, 0x5a, 0xe6, 0x73, 0xde,
    0x3b, 0x02, 0x03, 0x01, 0x00, 0x01,
];
