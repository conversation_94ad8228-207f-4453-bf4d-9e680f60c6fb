/*
   IMPORTANT: If you're editing this file in the output directory of one of
   your projects, your changes will be overwritten the next time you run
   Natural Docs.  Instead, copy this file to your project directory, make your
   changes, and you can use it with -s.  Even better would be to make a CSS
   file in your project directory with only your changes, which you can then
   use with -s [original style] [your changes].

   On the other hand, if you're editing this file in the Natural Docs styles
   directory, the changes will automatically be applied to all your projects
   that use this style the next time Natural Docs is run on them.

   This file is part of Natural Docs, which is Copyright (C) 2003-2008 Greg <PERSON>ure
   Natural Docs is licensed under the GPL
*/

body {
    font: 10pt Verdana, Arial, sans-serif;
    color: #000000;
    margin: 0; padding: 0;
    }

.ContentPage,
.IndexPage,
.FramedMenuPage {
    background-color: #E8E8E8;
    }
.FramedContentPage,
.FramedIndexPage,
.FramedSearchResultsPage,
.PopupSearchResultsPage {
    background-color: #FFFFFF;
    }


a:link,
a:visited { color: #900000; text-decoration: none }
a:hover { color: #900000; text-decoration: underline }
a:active { color: #FF0000; text-decoration: underline }

td {
    vertical-align: top }

img { border: 0;  }


/*
    Comment out this line to use web-style paragraphs (blank line between
    paragraphs, no indent) instead of print-style paragraphs (no blank line,
    indented.)
*/
p {
    text-indent: 5ex; margin: 0 }


/*  Can't use something like display: none or it won't break.  */
.HB {
    font-size: 1px;
    visibility: hidden;
    }

/*  Blockquotes are used as containers for things that may need to scroll.  */
blockquote {
    padding: 0;
    margin: 0;
    overflow: auto;
    }


.Firefox1 blockquote {
    padding-bottom: .5em;
    }

/*  Turn off scrolling when printing.  */
@media print {
    blockquote {
        overflow: visible;
        }
    .IE blockquote {
        width: auto;
        }
    }



#Menu {
    font-size: 9pt;
    padding: 10px 0 0 0;
    }
.ContentPage #Menu,
.IndexPage #Menu {
    position: absolute;
    top: 0;
    left: 0;
    width: 31ex;
    overflow: hidden;
    }
.ContentPage .Firefox #Menu,
.IndexPage .Firefox #Menu {
    width: 27ex;
    }


    .MTitle {
        font-size: 16pt; font-weight: bold; font-variant: small-caps;
        text-align: center;
        padding: 5px 10px 15px 10px;
        border-bottom: 1px dotted #000000;
        margin-bottom: 15px }

    .MSubTitle {
        font-size: 9pt; font-weight: normal; font-variant: normal;
        margin-top: 1ex; margin-bottom: 5px }


    .MEntry a:link,
    .MEntry a:hover,
    .MEntry a:visited { color: #606060; margin-right: 0 }
    .MEntry a:active { color: #A00000; margin-right: 0 }


    .MGroup {
        font-variant: small-caps; font-weight: bold;
        margin: 1em 0 1em 10px;
        }

    .MGroupContent {
        font-variant: normal; font-weight: normal }

    .MGroup a:link,
    .MGroup a:hover,
    .MGroup a:visited { color: #545454; margin-right: 10px }
    .MGroup a:active { color: #A00000; margin-right: 10px }


    .MFile,
    .MText,
    .MLink,
    .MIndex {
        padding: 1px 17px 2px 10px;
        margin: .25em 0 .25em 0;
        }

    .MText {
        font-size: 8pt; font-style: italic }

    .MLink {
        font-style: italic }

    #MSelected {
        color: #000000; background-color: #FFFFFF;
        /*  Replace padding with border.  */
        padding: 0 10px 0 10px;
        border-width: 1px 2px 2px 0; border-style: solid; border-color: #000000;
        margin-right: 5px;
        }

    /*  Close off the left side when its in a group.  */
    .MGroup #MSelected {
        padding-left: 9px; border-left-width: 1px }

    /*  A treat for Mozilla users.  Blatantly non-standard.  Will be replaced with CSS 3 attributes when finalized/supported.  */
    .Firefox #MSelected {
        -moz-border-radius-topright: 10px;
        -moz-border-radius-bottomright: 10px }
    .Firefox .MGroup #MSelected {
        -moz-border-radius-topleft: 10px;
        -moz-border-radius-bottomleft: 10px }


    #MSearchPanel {
        padding: 0px 6px;
        margin: .25em 0;
        }


    #MSearchField {
        font: italic 9pt Verdana, sans-serif;
        color: #606060;
        background-color: #E8E8E8;
        border: none;
        padding: 2px 4px;
        width: 100%;
        }
    /* Only Opera gets it right. */
    .Firefox #MSearchField,
    .IE #MSearchField,
    .Safari #MSearchField {
        width: 94%;
        }
    .Opera9 #MSearchField,
    .Konqueror #MSearchField {
        width: 97%;
        }
    .FramedMenuPage .Firefox #MSearchField,
    .FramedMenuPage .Safari #MSearchField,
    .FramedMenuPage .Konqueror #MSearchField {
        width: 98%;
        }

    /* Firefox doesn't do this right in frames without #MSearchPanel added on.
        It's presence doesn't hurt anything other browsers. */
    #MSearchPanel.MSearchPanelInactive:hover #MSearchField {
        background-color: #FFFFFF;
        border: 1px solid #C0C0C0;
        padding: 1px 3px;
        }
    .MSearchPanelActive #MSearchField {
        background-color: #FFFFFF;
        border: 1px solid #C0C0C0;
        font-style: normal;
        padding: 1px 3px;
        }

    #MSearchType {
        visibility: hidden;
        font: 8pt Verdana, sans-serif;
        width: 98%;
        padding: 0;
        border: 1px solid #C0C0C0;
        }
    .MSearchPanelActive #MSearchType,
    /*  As mentioned above, Firefox doesn't do this right in frames without #MSearchPanel added on. */
    #MSearchPanel.MSearchPanelInactive:hover #MSearchType,
    #MSearchType:focus {
        visibility: visible;
        color: #606060;
        }
    #MSearchType option#MSearchEverything {
        font-weight: bold;
        }

    .Opera8 .MSearchPanelInactive:hover,
    .Opera8 .MSearchPanelActive {
        margin-left: -1px;
        }


    iframe#MSearchResults {
        width: 60ex;
        height: 15em;
        }
    #MSearchResultsWindow {
        display: none;
        position: absolute;
        left: 0; top: 0;
        border: 1px solid #000000;
        background-color: #E8E8E8;
        }
    #MSearchResultsWindowClose {
        font-weight: bold;
        font-size: 8pt;
        display: block;
        padding: 2px 5px;
        }
    #MSearchResultsWindowClose:link,
    #MSearchResultsWindowClose:visited {
        color: #000000;
        text-decoration: none;
        }
    #MSearchResultsWindowClose:active,
    #MSearchResultsWindowClose:hover {
        color: #800000;
        text-decoration: none;
        background-color: #F4F4F4;
        }




#Content {
    padding-bottom: 15px;
    }

.ContentPage #Content {
    border-width: 0 0 1px 1px;
    border-style: solid;
    border-color: #000000;
    background-color: #FFFFFF;
    font-size: 9pt;  /* To make 31ex match the menu's 31ex. */
    margin-left: 31ex;
    }
.ContentPage .Firefox #Content {
    margin-left: 27ex;
    }



    .CTopic {
        font-size: 10pt;
        margin-bottom: 3em;
        }


    .CTitle {
        font-size: 12pt; font-weight: bold;
        border-width: 0 0 1px 0; border-style: solid; border-color: #A0A0A0;
        margin: 0 15px .5em 15px }

    .CGroup .CTitle {
        font-size: 16pt; font-variant: small-caps;
        padding-left: 15px; padding-right: 15px;
        border-width: 0 0 2px 0; border-color: #000000;
        margin-left: 0; margin-right: 0 }

    .CClass .CTitle,
    .CInterface .CTitle,
    .CDatabase .CTitle,
    .CDatabaseTable .CTitle,
    .CSection .CTitle {
        font-size: 18pt;
        color: #FFFFFF; background-color: #A0A0A0;
        padding: 10px 15px 10px 15px;
        border-width: 2px 0; border-color: #000000;
        margin-left: 0; margin-right: 0 }

    #MainTopic .CTitle {
        font-size: 20pt;
        color: #FFFFFF; background-color: #7070C0;
        padding: 10px 15px 10px 15px;
        border-width: 0 0 3px 0; border-color: #000000;
        margin-left: 0; margin-right: 0 }

    .CBody {
        margin-left: 15px; margin-right: 15px }


    .CToolTip {
        position: absolute; visibility: hidden;
        left: 0; top: 0;
        background-color: #FFFFE0;
        padding: 5px;
        border-width: 1px 2px 2px 1px; border-style: solid; border-color: #000000;
        font-size: 8pt;
        }

    .Opera .CToolTip {
        max-width: 98%;
        }

    /*  Scrollbars would be useless.  */
    .CToolTip blockquote {
        overflow: hidden;
        }
    .IE6 .CToolTip blockquote {
        overflow: visible;
        }

    .CHeading {
        font-weight: bold; font-size: 10pt;
        margin: 1.5em 0 .5em 0;
        }

    .CBody pre {
        font: 10pt "Courier New", Courier, monospace;
        margin: 1em 0;
        }

    .CBody ul {
        /*  I don't know why CBody's margin doesn't apply, but it's consistent across browsers so whatever.
             Reapply it here as padding.  */
        padding-left: 15px; padding-right: 15px;
        margin: .5em 5ex .5em 5ex;
        }

    .CDescriptionList {
        margin: .5em 5ex 0 5ex }

        .CDLEntry {
            font: 10pt "Courier New", Courier, monospace; color: #808080;
            padding-bottom: .25em;
            white-space: nowrap }

        .CDLDescription {
            font-size: 10pt;  /*  For browsers that don't inherit correctly, like Opera 5.  */
            padding-bottom: .5em; padding-left: 5ex }


    .CTopic img {
        text-align: center;
        display: block;
        margin: 1em auto;
        }
    .CImageCaption {
        font-variant: small-caps;
        font-size: 8pt;
        color: #808080;
        text-align: center;
        position: relative;
        top: 1em;
        }

    .CImageLink {
        color: #808080;
        font-style: italic;
        }
    a.CImageLink:link,
    a.CImageLink:visited,
    a.CImageLink:hover { color: #808080 }





.Prototype {
    font: 10pt "Courier New", Courier, monospace;
    padding: 5px 3ex;
    border-width: 1px; border-style: solid;
    margin: 0 5ex 1.5em 5ex;
    }

    .Prototype td {
        font-size: 10pt;
        }

    .PDefaultValue,
    .PDefaultValuePrefix,
    .PTypePrefix {
        color: #8F8F8F;
        }
    .PTypePrefix {
        text-align: right;
        }
    .PAfterParameters {
        vertical-align: bottom;
        }

    .IE .Prototype table {
        padding: 0;
        }

    .CFunction .Prototype {
        background-color: #F4F4F4; border-color: #D0D0D0 }
    .CProperty .Prototype {
        background-color: #F4F4FF; border-color: #C0C0E8 }
    .CVariable .Prototype {
        background-color: #FFFFF0; border-color: #E0E0A0 }

    .CClass .Prototype {
        border-width: 1px 2px 2px 1px; border-style: solid; border-color: #A0A0A0;
        background-color: #F4F4F4;
        }
    .CInterface .Prototype {
        border-width: 1px 2px 2px 1px; border-style: solid; border-color: #A0A0D0;
        background-color: #F4F4FF;
        }

    .CDatabaseIndex .Prototype,
    .CConstant .Prototype {
        background-color: #D0D0D0; border-color: #000000 }
    .CType .Prototype,
    .CEnumeration .Prototype {
        background-color: #FAF0F0; border-color: #E0B0B0;
        }
    .CDatabaseTrigger .Prototype,
    .CEvent .Prototype,
    .CDelegate .Prototype {
        background-color: #F0FCF0; border-color: #B8E4B8 }

    .CToolTip .Prototype {
        margin: 0 0 .5em 0;
        white-space: nowrap;
        }





.Summary {
    margin: 1.5em 5ex 0 5ex }

    .STitle {
        font-size: 12pt; font-weight: bold;
        margin-bottom: .5em }


    .SBorder {
        background-color: #FFFFF0;
        padding: 15px;
        border: 1px solid #C0C060 }

    /* In a frame IE 6 will make them too long unless you set the width to 100%.  Without frames it will be correct without a width
        or slightly too long (but not enough to scroll) with a width.  This arbitrary weirdness simply astounds me.  IE 7 has the same
        problem with frames, haven't tested it without.  */
    .FramedContentPage .IE .SBorder {
        width: 100% }

    /*  A treat for Mozilla users.  Blatantly non-standard.  Will be replaced with CSS 3 attributes when finalized/supported.  */
    .Firefox .SBorder {
        -moz-border-radius: 20px }


    .STable {
        font-size: 9pt; width: 100% }

    .SEntry {
        width: 30% }
    .SDescription {
        width: 70% }


    .SMarked {
        background-color: #F8F8D8 }

    .SDescription { padding-left: 2ex }
    .SIndent1 .SEntry { padding-left: 1.5ex }   .SIndent1 .SDescription { padding-left: 3.5ex }
    .SIndent2 .SEntry { padding-left: 3.0ex }   .SIndent2 .SDescription { padding-left: 5.0ex }
    .SIndent3 .SEntry { padding-left: 4.5ex }   .SIndent3 .SDescription { padding-left: 6.5ex }
    .SIndent4 .SEntry { padding-left: 6.0ex }   .SIndent4 .SDescription { padding-left: 8.0ex }
    .SIndent5 .SEntry { padding-left: 7.5ex }   .SIndent5 .SDescription { padding-left: 9.5ex }

    .SDescription a { color: #800000}
    .SDescription a:active { color: #A00000 }

    .SGroup td {
        padding-top: .5em; padding-bottom: .25em }

    .SGroup .SEntry {
        font-weight: bold; font-variant: small-caps }

    .SGroup .SEntry a { color: #800000 }
    .SGroup .SEntry a:active { color: #F00000 }


    .SMain td,
    .SClass td,
    .SDatabase td,
    .SDatabaseTable td,
    .SSection td {
        font-size: 10pt;
        padding-bottom: .25em }

    .SClass td,
    .SDatabase td,
    .SDatabaseTable td,
    .SSection td {
        padding-top: 1em }

    .SMain .SEntry,
    .SClass .SEntry,
    .SDatabase .SEntry,
    .SDatabaseTable .SEntry,
    .SSection .SEntry {
        font-weight: bold;
        }

    .SMain .SEntry a,
    .SClass .SEntry a,
    .SDatabase .SEntry a,
    .SDatabaseTable .SEntry a,
    .SSection .SEntry a { color: #000000 }

    .SMain .SEntry a:active,
    .SClass .SEntry a:active,
    .SDatabase .SEntry a:active,
    .SDatabaseTable .SEntry a:active,
    .SSection .SEntry a:active { color: #A00000 }





.ClassHierarchy {
    margin: 0 15px 1em 15px }

    .CHEntry {
        border-width: 1px 2px 2px 1px; border-style: solid; border-color: #A0A0A0;
        margin-bottom: 3px;
        padding: 2px 2ex;
        font-size: 10pt;
        background-color: #F4F4F4; color: #606060;
        }

    .Firefox .CHEntry {
        -moz-border-radius: 4px;
        }

    .CHCurrent .CHEntry {
        font-weight: bold;
        border-color: #000000;
        color: #000000;
        }

    .CHChildNote .CHEntry {
        font-style: italic;
        font-size: 8pt;
        }

    .CHIndent {
        margin-left: 3ex;
        }

    .CHEntry a:link,
    .CHEntry a:visited,
    .CHEntry a:hover {
        color: #606060;
        }
    .CHEntry a:active {
        color: #800000;
        }





#Index {
    background-color: #FFFFFF;
    }

/*  As opposed to .PopupSearchResultsPage #Index  */
.IndexPage #Index,
.FramedIndexPage #Index,
.FramedSearchResultsPage #Index {
    padding: 15px;
    }

.IndexPage #Index {
    border-width: 0 0 1px 1px;
    border-style: solid;
    border-color: #000000;
    font-size: 9pt;  /* To make 27ex match the menu's 27ex. */
    margin-left: 27ex;
    }


    .IPageTitle {
        font-size: 20pt; font-weight: bold;
        color: #FFFFFF; background-color: #7070C0;
        padding: 10px 15px 10px 15px;
        border-width: 0 0 3px 0; border-color: #000000; border-style: solid;
        margin: -15px -15px 0 -15px }

    .FramedSearchResultsPage .IPageTitle {
        margin-bottom: 15px;
        }

    .INavigationBar {
        font-size: 10pt;
        text-align: center;
        background-color: #FFFFF0;
        padding: 5px;
        border-bottom: solid 1px black;
        margin: 0 -15px 15px -15px;
        }

    .INavigationBar a {
        font-weight: bold }

    .IHeading {
        font-size: 16pt; font-weight: bold;
        padding: 2.5em 0 .5em 0;
        text-align: center;
        width: 3.5ex;
        }
    #IFirstHeading {
        padding-top: 0;
        }

    .IEntry {
        font-size: 10pt;
        padding-left: 1ex;
        }
    .PopupSearchResultsPage .IEntry {
        font-size: 8pt;
        padding: 1px 5px;
        }
    .PopupSearchResultsPage .Opera9 .IEntry,
    .FramedSearchResultsPage .Opera9 .IEntry {
        text-align: left;
        }
    .FramedSearchResultsPage .IEntry {
        padding: 0;
        }

    .ISubIndex {
        padding-left: 3ex; padding-bottom: .5em }
    .PopupSearchResultsPage .ISubIndex {
        display: none;
        }

    /*  While it may cause some entries to look like links when they aren't, I found it's much easier to read the
         index if everything's the same color.  */
    .ISymbol {
        font-weight: bold; color: #900000  }

    .IndexPage .ISymbolPrefix,
    .FramedIndexPage .ISymbolPrefix {
        font-size: 10pt;
        text-align: right;
        color: #C47C7C;
        background-color: #F8F8F8;
        border-right: 3px solid #E0E0E0;
        border-left: 1px solid #E0E0E0;
        padding: 0 1px 0 2px;
        }
    .PopupSearchResultsPage .ISymbolPrefix,
    .FramedSearchResultsPage .ISymbolPrefix {
        color: #900000;
        }
    .PopupSearchResultsPage .ISymbolPrefix {
        font-size: 8pt;
        }

    .IndexPage #IFirstSymbolPrefix,
    .FramedIndexPage #IFirstSymbolPrefix {
        border-top: 1px solid #E0E0E0;
        }
    .IndexPage #ILastSymbolPrefix,
    .FramedIndexPage #ILastSymbolPrefix {
        border-bottom: 1px solid #E0E0E0;
        }
    .IndexPage #IOnlySymbolPrefix,
    .FramedIndexPage #IOnlySymbolPrefix {
        border-top: 1px solid #E0E0E0;
        border-bottom: 1px solid #E0E0E0;
        }

    a.IParent,
    a.IFile {
        display: block;
        }

    .PopupSearchResultsPage .SRStatus {
        padding: 2px 5px;
        font-size: 8pt;
        font-style: italic;
        }
    .FramedSearchResultsPage .SRStatus {
        font-size: 10pt;
        font-style: italic;
        }

    .SRResult {
        display: none;
        }



#Footer {
    font-size: 8pt;
    color: #989898;
    text-align: right;
    }

#Footer p {
    text-indent: 0;
    margin-bottom: .5em;
    }

.ContentPage #Footer,
.IndexPage #Footer {
    text-align: right;
    margin: 2px;
    }

.FramedMenuPage #Footer {
    text-align: center;
    margin: 5em 10px 10px 10px;
    padding-top: 1em;
    border-top: 1px solid #C8C8C8;
    }

    #Footer a:link,
    #Footer a:hover,
    #Footer a:visited { color: #989898 }
    #Footer a:active { color: #A00000 }

