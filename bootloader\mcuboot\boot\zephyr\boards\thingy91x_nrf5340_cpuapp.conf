# MCUBoot settings
CONFIG_BOOT_MAX_IMG_SECTORS=110

# MCUboot serial recovery
CONFIG_MCUBOOT_SERIAL=y

# Disable <PERSON><PERSON>hyr console
CONFIG_LOG=n
CONFIG_CONSOLE=n
CONFIG_CONSOLE_HANDLER=n
CONFIG_UART_CONSOLE=n

# Serial
CONFIG_SERIAL=y
CONFIG_UART_NRFX=y
CONFIG_UART_INTERRUPT_DRIVEN=y
CONFIG_UART_LINE_CTRL=y

# MCUboot serial recovery
CONFIG_GPIO=y
CONFIG_MCUBOOT_SERIAL=y
CONFIG_BOOT_SERIAL_CDC_ACM=y

# Required by USB
CONFIG_MULTITHREADING=y

# USB
CONFIG_USB_DEVICE_STACK=y
CONFIG_USB_DEVICE_PRODUCT="MCUBOOT"
CONFIG_USB_CDC_ACM=y
CONFIG_USB_COMPOSITE_DEVICE=y
CONFIG_USB_MASS_STORAGE=n
CONFIG_USB_DEVICE_MANUFACTURER="Nordic Semiconductor"
CONFIG_USB_DEVICE_VID=0x1915
CONFIG_USB_DEVICE_PID=0x910A

CONFIG_BOOT_SERIAL_BOOT_MODE=y

CONFIG_PM_PARTITION_SIZE_MCUBOOT=0x13E00

# The following configurations are required to support simultaneous multi image update
CONFIG_PCD_APP=y
CONFIG_UPDATEABLE_IMAGE_NUMBER=2
CONFIG_BOOT_UPGRADE_ONLY=y
# The network core cannot access external flash directly. The flash simulator must be used to
# provide a memory region that is used to forward the new firmware to the network core.
CONFIG_FLASH_SIMULATOR=y
CONFIG_FLASH_SIMULATOR_DOUBLE_WRITES=y
CONFIG_FLASH_SIMULATOR_STATS=n

CONFIG_BOOT_IMAGE_ACCESS_HOOKS=y

# Makes it possible to update the network core using the flash simulator
CONFIG_NRF53_RECOVERY_NETWORK_CORE=y

CONFIG_MCUBOOT_SERIAL_DIRECT_IMAGE_UPLOAD=y
CONFIG_BOOT_SERIAL_IMG_GRP_IMAGE_STATE=y

# Skip checks on the secondary image to make it possible to update MCUBoot on S1/S0
CONFIG_MCUBOOT_VERIFY_IMG_ADDRESS=n

CONFIG_BOOT_SERIAL_NO_APPLICATION=y
CONFIG_FW_INFO_FIRMWARE_VERSION=2
