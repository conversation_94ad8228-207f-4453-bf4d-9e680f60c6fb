#
# Copyright (c) 2021 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

# These configurations should be used when using nrf/samples/bootloader
# as the immutable bootloader (B0), and <PERSON><PERSON><PERSON><PERSON> as the second stage updateable
# bootloader.

# Set ECDSA as signing mechanism
CONFIG_BOOT_SIGNATURE_TYPE_ECDSA_P256=y

# Use crypto backend from B0
CONFIG_BOOT_NRF_EXTERNAL_CRYPTO=y
CONFIG_SECURE_BOOT_CRYPTO=y
CONFIG_SB_CRYPTO_CLIENT_ECDSA_SECP256R1=y
CONFIG_SB_CRYPTO_CLIENT_SHA256=y
CONFIG_BL_SHA256_EXT_API_REQUIRED=y
CONFIG_BL_SECP256R1_EXT_API_REQUIRED=y
