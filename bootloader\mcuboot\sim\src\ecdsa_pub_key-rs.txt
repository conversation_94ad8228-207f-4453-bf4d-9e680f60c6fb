static ECDSA256_PUB_KEY: &[u8] = &[
    0x30, 0x59, 0x30, 0x13, 0x06, 0x07, 0x2a, 0x86,
    0x48, 0xce, 0x3d, 0x02, 0x01, 0x06, 0x08, 0x2a,
    0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07, 0x03,
    0x42, 0x00, 0x04, 0x2a, 0xcb, 0x40, 0x3c, 0xe8,
    0xfe, 0xed, 0x5b, 0xa4, 0x49, 0x95, 0xa1, 0xa9,
    0x1d, 0xae, 0xe8, 0xdb, 0xbe, 0x19, 0x37, 0xcd,
    0x14, 0xfb, 0x2f, 0x24, 0x57, 0x37, 0xe5, 0x95,
    0x39, 0x88, 0xd9, 0x94, 0xb9, 0xd6, 0x5a, 0xeb,
    0xd7, 0xcd, 0xd5, 0x30, 0x8a, 0xd6, 0xfe, 0x48,
    0xb2, 0x4a, 0x6a, 0x81, 0x0e, 0xe5, 0xf0, 0x7d,
    0x8b, 0x68, 0x34, 0xcc, 0x3a, 0x6a, 0xfc, 0x53,
    0x8e, 0xfa, 0xc1,
];

static ECDSAP384_PUB_KEY: &[u8] = &[
    0x30, 0x76, 0x30, 0x10, 0x06, 0x07, 0x2a, 0x86,
    0x48, 0xce, 0x3d, 0x02, 0x01, 0x06, 0x05, 0x2b,
    0x81, 0x04, 0x00, 0x22, 0x03, 0x62, 0x00, 0x04,
    0x0c, 0x76, 0xca, 0xae, 0x72, 0x3a, 0xa5, 0xe8,
    0xf0, 0xd4, 0xf1, 0x16, 0xb5, 0x02, 0xef, 0x77,
    0xa1, 0x1b, 0x93, 0x61, 0x78, 0xc0, 0x09, 0x26,
    0x7b, 0x3b, 0x40, 0x9c, 0xee, 0x49, 0x85, 0xe0,
    0xc9, 0x4f, 0xe7, 0xf2, 0xba, 0x97, 0x6c, 0xf3,
    0x82, 0x65, 0x14, 0x2c, 0xf5, 0x0c, 0x73, 0x33,
    0x4d, 0x32, 0xe7, 0x9b, 0xd3, 0x42, 0xcc, 0x95,
    0x5a, 0xe5, 0xe2, 0xf5, 0xf4, 0x6e, 0x45, 0xe0,
    0xed, 0x20, 0x35, 0x5c, 0xaf, 0x52, 0x35, 0x81,
    0xd4, 0xdc, 0x9c, 0xe3, 0x9e, 0x22, 0x3e, 0xfb,
    0x3f, 0x22, 0x10, 0xda, 0x70, 0x03, 0x37, 0xad,
    0xa8, 0xf2, 0x48, 0xfe, 0x3a, 0x60, 0x69, 0xa5,
];
