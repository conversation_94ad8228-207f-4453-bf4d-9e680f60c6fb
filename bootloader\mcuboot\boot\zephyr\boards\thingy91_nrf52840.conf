# Disable <PERSON><PERSON><PERSON>r console
CONFIG_LOG=n
CONFIG_CONSOLE=n
CONFIG_CONSOLE_HANDLER=n
CONFIG_UART_CONSOLE=n

# The build won't fit on the partition allocated for it without size
# optimizations.
CONFIG_SIZE_OPTIMIZATIONS=y
CONFIG_PM_PARTITION_SIZE_MCUBOOT=0x12000

# Serial
CONFIG_SERIAL=y
CONFIG_UART_NRFX=y
CONFIG_UART_INTERRUPT_DRIVEN=y
CONFIG_UART_LINE_CTRL=y

# MCUboot serial recovery
CONFIG_GPIO=y
CONFIG_MCUBOOT_SERIAL=y
CONFIG_BOOT_SERIAL_CDC_ACM=y

# Required by USB
CONFIG_MULTITHREADING=y

# USB
CONFIG_USB_DEVICE_STACK=y
CONFIG_USB_DEVICE_PRODUCT="MCUBOOT"
CONFIG_USB_CDC_ACM=y
CONFIG_USB_COMPOSITE_DEVICE=y
CONFIG_USB_MASS_STORAGE=n
CONFIG_USB_DEVICE_MANUFACTURER="Nordic Semiconductor"
CONFIG_USB_DEVICE_VID=0x1915
CONFIG_USB_DEVICE_PID=0x520F
