# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

$import:
    - '@mcuboot/ci/mynewt_targets/basic/syscfg.yml'

syscfg.vals:
    BOOTUTIL_VALIDATE_SLOT0: 1
    BOOTUTIL_SIGN_EC256: 0
    BOOTUTIL_SIGN_RSA: 1
    BOOTUTIL_USE_MBED_TLS: 1
    BOOTUTIL_USE_TINYCRYPT: 0
    BOOTUTIL_ENCRYPT_KW: 1
    MBEDTLS_NIST_KW_C: 1
    MBEDTLS_CIPHER_MODE_CTR: 1
