<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>Function Index - CoreMark</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="IndexPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=IPageTitle>Function Index</div><div class=INavigationBar>$#! &middot; 0-9 &middot; A &middot; B &middot; <a href="#C">C</a> &middot; D &middot; E &middot; F &middot; <a href="#G">G</a> &middot; H &middot; <a href="#I">I</a> &middot; J &middot; K &middot; L &middot; <a href="#M">M</a> &middot; N &middot; O &middot; <a href="#P">P</a> &middot; Q &middot; R &middot; <a href="#S">S</a> &middot; <a href="#T">T</a> &middot; U &middot; V &middot; W &middot; X &middot; Y &middot; Z</div><table border=0 cellspacing=0 cellpadding=0><tr><td class=IHeading id=IFirstHeading><a name="C"></a>C</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#cmp_complex" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')" class=ISymbol>cmp_complex</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#cmp_idx" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')" class=ISymbol>cmp_idx</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#core_bench_matrix" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')" class=ISymbol>core_bench_matrix</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_state-c.html#core_bench_state" id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')" class=ISymbol>core_bench_state</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_state-c.html#core_init_state" id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')" class=ISymbol>core_init_state</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_find" id=link6 onMouseOver="ShowTip(event, 'tt6', 'link6')" onMouseOut="HideTip('tt6')" class=ISymbol>core_list_find</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_init" id=link7 onMouseOver="ShowTip(event, 'tt7', 'link7')" onMouseOut="HideTip('tt7')" class=ISymbol>core_list_init</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_insert" id=link8 onMouseOver="ShowTip(event, 'tt8', 'link8')" onMouseOut="HideTip('tt8')" class=ISymbol>core_list_insert</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_mergesort" id=link9 onMouseOver="ShowTip(event, 'tt9', 'link9')" onMouseOut="HideTip('tt9')" class=ISymbol>core_list_mergesort</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_remove" id=link10 onMouseOver="ShowTip(event, 'tt10', 'link10')" onMouseOut="HideTip('tt10')" class=ISymbol>core_list_remove</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_reverse" id=link11 onMouseOver="ShowTip(event, 'tt11', 'link11')" onMouseOut="HideTip('tt11')" class=ISymbol>core_list_reverse</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_list_join-c.html#core_list_undo_remove" id=link12 onMouseOver="ShowTip(event, 'tt12', 'link12')" onMouseOut="HideTip('tt12')" class=ISymbol>core_list_undo_remove</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#core_start_parallel" id=link13 onMouseOver="ShowTip(event, 'tt13', 'link13')" onMouseOut="HideTip('tt13')" class=ISymbol>core_start_parallel</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_state-c.html#core_state_transition" id=link14 onMouseOver="ShowTip(event, 'tt14', 'link14')" onMouseOut="HideTip('tt14')" class=ISymbol>core_state_transition</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#core_stop_parallel" id=link15 onMouseOver="ShowTip(event, 'tt15', 'link15')" onMouseOut="HideTip('tt15')" class=ISymbol>core_stop_parallel</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_util-c.html#crc*" id=link16 onMouseOver="ShowTip(event, 'tt16', 'link16')" onMouseOut="HideTip('tt16')" class=ISymbol>crc*</a></td></tr><tr><td class=IHeading><a name="G"></a>G</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_util-c.html#get_seed" id=link17 onMouseOver="ShowTip(event, 'tt17', 'link17')" onMouseOut="HideTip('tt17')" class=ISymbol>get_seed</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#get_time" id=link18 onMouseOver="ShowTip(event, 'tt18', 'link18')" onMouseOut="HideTip('tt18')" class=ISymbol>get_time</a></td></tr><tr><td class=IHeading><a name="I"></a>I</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_main-c.html#iterate" id=link19 onMouseOver="ShowTip(event, 'tt19', 'link19')" onMouseOut="HideTip('tt19')" class=ISymbol>iterate</a></td></tr><tr><td class=IHeading><a name="M"></a>M</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_main-c.html#main" id=link20 onMouseOver="ShowTip(event, 'tt20', 'link20')" onMouseOut="HideTip('tt20')" class=ISymbol>main</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_add_const" id=link21 onMouseOver="ShowTip(event, 'tt21', 'link21')" onMouseOut="HideTip('tt21')" class=ISymbol>matrix_add_const</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_mul_const" id=link22 onMouseOver="ShowTip(event, 'tt22', 'link22')" onMouseOut="HideTip('tt22')" class=ISymbol>matrix_mul_const</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_mul_matrix" id=link23 onMouseOver="ShowTip(event, 'tt23', 'link23')" onMouseOut="HideTip('tt23')" class=ISymbol>matrix_mul_matrix</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_mul_matrix_bitextract" id=link24 onMouseOver="ShowTip(event, 'tt24', 'link24')" onMouseOut="HideTip('tt24')" class=ISymbol>matrix_mul_matrix_bitextract</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_mul_vect" id=link25 onMouseOver="ShowTip(event, 'tt25', 'link25')" onMouseOut="HideTip('tt25')" class=ISymbol>matrix_mul_vect</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_sum" id=link26 onMouseOver="ShowTip(event, 'tt26', 'link26')" onMouseOut="HideTip('tt26')" class=ISymbol>matrix_sum</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/core_matrix-c.html#matrix_test" id=link27 onMouseOver="ShowTip(event, 'tt27', 'link27')" onMouseOut="HideTip('tt27')" class=ISymbol>matrix_test</a></td></tr><tr><td class=IHeading><a name="P"></a>P</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#portable_fini" id=link28 onMouseOver="ShowTip(event, 'tt28', 'link28')" onMouseOut="HideTip('tt28')" class=ISymbol>portable_fini</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#portable_free" id=link29 onMouseOver="ShowTip(event, 'tt29', 'link29')" onMouseOut="HideTip('tt29')" class=ISymbol>portable_free</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#portable_init" id=link30 onMouseOver="ShowTip(event, 'tt30', 'link30')" onMouseOut="HideTip('tt30')" class=ISymbol>portable_init</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#portable_malloc" id=link31 onMouseOver="ShowTip(event, 'tt31', 'link31')" onMouseOut="HideTip('tt31')" class=ISymbol>portable_malloc</a></td></tr><tr><td class=IHeading><a name="S"></a>S</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#start_time" id=link32 onMouseOver="ShowTip(event, 'tt32', 'link32')" onMouseOut="HideTip('tt32')" class=ISymbol>start_time</a></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#stop_time" id=link33 onMouseOver="ShowTip(event, 'tt33', 'link33')" onMouseOut="HideTip('tt33')" class=ISymbol>stop_time</a></td></tr><tr><td class=IHeading><a name="T"></a>T</td><td></td></tr><tr><td class=ISymbolPrefix id=IOnlySymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/linux/core_portme-c.html#time_in_secs" id=link34 onMouseOver="ShowTip(event, 'tt34', 'link34')" onMouseOut="HideTip('tt34')" class=ISymbol>time_in_secs</a></td></tr></table>
<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s32 cmp_complex(</td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>a,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>b,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Compare the data item in a list cell.</div></div><div class=CToolTip id="tt2"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s32 cmp_idx(</td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>a,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>b,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Compare the idx item in a list cell, and regen the data.</div></div><div class=CToolTip id="tt3"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_u16 core_bench_matrix(</td><td class=PType nowrap>mat_params&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed,</td></tr><tr><td></td><td class=PType nowrap>ee_u16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>crc</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Benchmark function</div></div><div class=CToolTip id="tt4"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_u16 core_bench_state(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>blksize,</td></tr><tr><td></td><td class=PType nowrap>ee_u8&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed1,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed2,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>step,</td></tr><tr><td></td><td class=PType nowrap>ee_u16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>crc</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Benchmark function</div></div><div class=CToolTip id="tt5"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void core_init_state(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>size,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed,</td></tr><tr><td></td><td class=PType nowrap>ee_u8&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Initialize the input data for the state machine.</div></div><div class=CToolTip id="tt6"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_find(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list,</td></tr><tr><td></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>info</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Find an item in the list</div></div><div class=CToolTip id="tt7"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_init(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>blksize,</td></tr><tr><td></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PType nowrap>ee_s16&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>seed</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Initialize list with data.</div></div><div class=CToolTip id="tt8"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_insert_new(</td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>insert_point,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>info,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>**</td><td class=PParameter nowrap>memblock,</td></tr><tr><td></td><td class=PTypePrefix nowrap>list_data&nbsp;</td><td class=PType nowrap>**datablock&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>memblock_end,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>list_data&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>datablock_end</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Insert an item to the list</div></div><div class=CToolTip id="tt9"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_mergesort(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list,</td></tr><tr><td></td><td class=PType nowrap>list_cmp&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>cmp,</td></tr><tr><td></td><td class=PType nowrap>core_results&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>res</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Sort the list in place without recursion.</div></div><div class=CToolTip id="tt10"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_remove(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Remove an item from the list.</div></div><div class=CToolTip id="tt11"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_reverse(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>list</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Reverse a list</div></div><div class=CToolTip id="tt12"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>list_head *core_list_undo_remove(</td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item_removed,</td></tr><tr><td></td><td class=PType nowrap>list_head&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>item_modified</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Undo a remove operation.</div></div><div class=CToolTip id="tt13"><div class=CFunction>Start benchmarking in a parallel context.</div></div><div class=CToolTip id="tt14"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>enum CORE_STATE core_state_transition(</td><td class=PTypePrefix nowrap>ee_u8&nbsp;</td><td class=PType nowrap>**instr&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>,</td></tr><tr><td></td><td class=PTypePrefix nowrap></td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>transition_count</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Actual state machine.</div></div><div class=CToolTip id="tt15"><div class=CFunction>Stop a parallel context execution of coremark, and gather the results.</div></div><div class=CToolTip id="tt16"><div class=CFunction>Service functions to calculate 16b CRC code.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt17"><div class=CFunction>Get a values that cannot be determined at compile time.</div></div><div class=CToolTip id="tt18"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>CORE_TICKS get_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Return an abstract &ldquo;ticks&rdquo; number that signifies time on the system.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt19"><div class=CFunction>Run the benchmark for a specified number of iterations.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt20"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>#if MAIN_HAS_NOARGC MAIN_RETURN_TYPE main(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Main entry routine for the benchmark. </div></div><div class=CToolTip id="tt21"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_add_const(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Add a constant value to all elements of a matrix.</div></div><div class=CToolTip id="tt22"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_const(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a constant. </div></div><div class=CToolTip id="tt23"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_matrix(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a matrix. </div></div><div class=CToolTip id="tt24"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_matrix_bitextract(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a matrix, and extract some bits from the result. </div></div><div class=CToolTip id="tt25"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void matrix_mul_vect(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Multiply a matrix by a vector. </div></div><div class=CToolTip id="tt26"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s16 matrix_sum(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>clipval</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Calculate a function that depends on the values of elements in the matrix.</div></div><div class=CToolTip id="tt27"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>ee_s16 matrix_test(</td><td class=PType nowrap>ee_u32&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>N,</td></tr><tr><td></td><td class=PType nowrap>MATRES&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>C,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>A,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>B,</td></tr><tr><td></td><td class=PType nowrap>MATDAT&nbsp;</td><td class=PParameterPrefix nowrap></td><td class=PParameter nowrap>val</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Perform matrix manipulation.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt28"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_fini(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Target specific final code</div></div><div class=CToolTip id="tt29"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_free(</td><td class=PType nowrap>void&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Provide free() functionality in a platform specific way.</div></div><div class=CToolTip id="tt30"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_init(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>int&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argc,</td></tr><tr><td></td><td class=PType nowrap>char&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argv[]</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Target specific initialization code Test for some common mistakes.</div></div><div class=CToolTip id="tt31"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void *portable_malloc(</td><td class=PType nowrap>size_t&nbsp;</td><td class=PParameter nowrap>size</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Provide malloc() functionality in a platform specific way.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt32"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void start_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>This function will be called right before starting the timed portion of the benchmark.</div></div><div class=CToolTip id="tt33"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void stop_time(</td><td class=PParameter nowrap>void</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>This function will be called right after ending the timed portion of the benchmark.</div></div><!--END_ND_TOOLTIPS-->


<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt34"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>secs_ret time_in_secs(</td><td class=PType nowrap>CORE_TICKS&nbsp;</td><td class=PParameter nowrap>ticks</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Convert the value returned by get_time to seconds.</div></div><!--END_ND_TOOLTIPS-->

</div><!--Index-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="../files/readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="../files/release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../files/core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-h.html">PORT_DIR/<span class=HB> </span>core_portme.h</a></div></div><div class=MEntry><div class=MFile><a href="../files/linux/core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="Files.html">Files</a></div></div><div class=MEntry><div class=MIndex id=MSelected>Functions</div></div><div class=MEntry><div class=MIndex><a href="Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->


<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>