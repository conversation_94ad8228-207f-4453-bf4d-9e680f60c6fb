#include <bootutil/sign_key.h>
static unsigned char key[] = {
  0x30, 0x59, 0x30, 0x13, 0x06, 0x07, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x02,
  0x01, 0x06, 0x08, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07, 0x03,
  0x42, 0x00, 0x04, 0x6e, 0x68, 0x61, 0x7f, 0xd7, 0xe2, 0xc9, 0x3d, 0xb4,
  0x0a, 0x2c, 0x6d, 0xb0, 0xd0, 0xce, 0xfe, 0x54, 0xb8, 0x96, 0xad, 0xfc,
  0x60, 0x87, 0x89, 0x41, 0xe9, 0x2d, 0x21, 0xc8, 0x3e, 0x1d, 0x5e, 0xc3,
  0x77, 0xfd, 0x62, 0x5f, 0xbb, 0xfb, 0xda, 0x10, 0xa7, 0x5a, 0x23, 0xaa,
  0xfc, 0x01, 0xb5, 0xdc, 0x73, 0x3d, 0x37, 0xc1, 0x45, 0x8f, 0x90, 0x3b,
  0x66, 0xfd, 0x2b, 0x95, 0x66, 0x9c, 0xc9
};
static unsigned int key_len = 91;
const struct bootutil_key bootutil_keys[] = {
    [0] = {
        .key = key,
        .len = &key_len,
    },
};
const int bootutil_key_cnt = 1;
