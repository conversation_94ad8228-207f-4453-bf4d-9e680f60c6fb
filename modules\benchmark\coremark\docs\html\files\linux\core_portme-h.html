<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><title>core_portme.h - CoreMark</title><link rel="stylesheet" type="text/css" href="../../styles/main.css"><script language=JavaScript src="../../javascript/main.js"></script><script language=JavaScript src="../../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CFile"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="core_portme.h"></a>core_portme.h</h1><div class=CBody><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#core_portme.h" >core_portme.h</a></td><td class=SDescription></td></tr><tr class="SGeneric SMarked"><td class=SEntry><a href="#Description" >Description</a></td><td class=SDescription>This file contains configuration constants required to execute on different platforms</td></tr><tr class="SGroup"><td class=SEntry><a href="#Configuration" >Configuration</a></td><td class=SDescription></td></tr><tr class="SConfiguration SIndent1 SMarked"><td class=SEntry><a href="#HAS_FLOAT" >HAS_FLOAT</a></td><td class=SDescription>Define to 1 if the platform supports floating point.</td></tr><tr class="SConfiguration SIndent1"><td class=SEntry><a href="#HAS_TIME_H" >HAS_TIME_H</a></td><td class=SDescription>Define to 1 if platform has the time.h header file, and implementation of functions thereof.</td></tr><tr class="SConfiguration SIndent1 SMarked"><td class=SEntry><a href="#USE_CLOCK" >USE_CLOCK</a></td><td class=SDescription>Define to 1 if platform has the time.h header file, and implementation of functions thereof.</td></tr><tr class="SConfiguration SIndent1"><td class=SEntry><a href="#HAS_STDIO" >HAS_STDIO</a></td><td class=SDescription>Define to 1 if the platform has stdio.h.</td></tr><tr class="SConfiguration SIndent1 SMarked"><td class=SEntry><a href="#HAS_PRINTF" >HAS_PRINTF</a></td><td class=SDescription>Define to 1 if the platform has stdio.h and implements the printf function.</td></tr><tr class="SConfiguration SIndent1"><td class=SEntry><a href="#CORE_TICKS" >CORE_TICKS</a></td><td class=SDescription>Define type of return from the timing functions.</td></tr><tr class="SConfiguration SIndent1 SMarked"><td class=SEntry><a href="#SEED_METHOD" >SEED_METHOD</a></td><td class=SDescription>Defines method to get seed values that cannot be computed at compile time.</td></tr><tr class="SConfiguration SIndent1"><td class=SEntry><a href="#MEM_METHOD" >MEM_METHOD</a></td><td class=SDescription>Defines method to get a block of memry.</td></tr><tr class="SConfiguration SIndent1 SMarked"><td class=SEntry><a href="#MULTITHREAD" >MULTITHREAD</a></td><td class=SDescription>Define for parallel execution</td></tr><tr class="SConfiguration SIndent1"><td class=SEntry><a href="#USE_PTHREAD" >USE_PTHREAD</a></td><td class=SDescription>Sample implementation for launching parallel contexts This implementation uses pthread_thread_create and pthread_join.</td></tr><tr class="SConfiguration SIndent1 SMarked"><td class=SEntry><a href="#USE_FORK" >USE_FORK</a></td><td class=SDescription>Sample implementation for launching parallel contexts This implementation uses fork, waitpid, shmget,shmat and shmdt.</td></tr><tr class="SConfiguration SIndent1"><td class=SEntry><a href="#USE_SOCKET" >USE_SOCKET</a></td><td class=SDescription>Sample implementation for launching parallel contexts This implementation uses fork, socket, sendto and recvfrom</td></tr><tr class="SConfiguration SIndent1 SMarked"><td class=SEntry><a href="#MAIN_HAS_NOARGC" >MAIN_HAS_NOARGC</a></td><td class=SDescription>Needed if platform does not support getting arguments to main.</td></tr><tr class="SConfiguration SIndent1"><td class=SEntry><a href="#MAIN_HAS_NORETURN" >MAIN_HAS_NORETURN</a></td><td class=SDescription>Needed if platform does not support returning a value from main.</td></tr><tr class="SGroup"><td class=SEntry><a href="#Variables" >Variables</a></td><td class=SDescription></td></tr><tr class="SVariable SIndent1 SMarked"><td class=SEntry><a href="#default_num_contexts" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">default_num_contexts</a></td><td class=SDescription>Number of contexts to spawn in multicore context. </td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGeneric"><div class=CTopic><h3 class=CTitle><a name="Description"></a>Description</h3><div class=CBody><p>This file contains configuration constants required to execute on different platforms</p></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="Configuration"></a>Configuration</h3></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="HAS_FLOAT"></a>HAS_FLOAT</h3><div class=CBody><p>Define to 1 if the platform supports floating point.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="HAS_TIME_H"></a>HAS_TIME_H</h3><div class=CBody><p>Define to 1 if platform has the time.h header file, and implementation of functions thereof.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="USE_CLOCK"></a>USE_CLOCK</h3><div class=CBody><p>Define to 1 if platform has the time.h header file, and implementation of functions thereof.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="HAS_STDIO"></a>HAS_STDIO</h3><div class=CBody><p>Define to 1 if the platform has stdio.h.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="HAS_PRINTF"></a>HAS_PRINTF</h3><div class=CBody><p>Define to 1 if the platform has stdio.h and implements the printf function.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="CORE_TICKS"></a>CORE_TICKS</h3><div class=CBody><p>Define type of return from the timing functions.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="SEED_METHOD"></a>SEED_METHOD</h3><div class=CBody><p>Defines method to get seed values that cannot be computed at compile time.</p><h4 class=CHeading>Valid values</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>SEED_ARG</td><td class=CDLDescription>from command line.</td></tr><tr><td class=CDLEntry>SEED_FUNC</td><td class=CDLDescription>from a system function.</td></tr><tr><td class=CDLEntry>SEED_VOLATILE</td><td class=CDLDescription>from volatile variables.</td></tr></table></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="MEM_METHOD"></a>MEM_METHOD</h3><div class=CBody><p>Defines method to get a block of memry.</p><h4 class=CHeading>Valid values</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>MEM_MALLOC</td><td class=CDLDescription>for platforms that implement malloc and have malloc.h.</td></tr><tr><td class=CDLEntry>MEM_STATIC</td><td class=CDLDescription>to use a static memory array.</td></tr><tr><td class=CDLEntry>MEM_STACK</td><td class=CDLDescription>to allocate the data block on the stack (NYI).</td></tr></table></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="MULTITHREAD"></a>MULTITHREAD</h3><div class=CBody><p>Define for parallel execution</p><h4 class=CHeading>Valid values</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>1</td><td class=CDLDescription>only one context (default).</td></tr><tr><td class=CDLEntry>N&gt;1</td><td class=CDLDescription>will execute N copies in parallel.</td></tr></table><h4 class=CHeading>Note</h4><p>If this flag is defined to more then 1, an implementation for launching parallel contexts must be defined.</p><p>Two sample implementations are provided.&nbsp; Use <a href="#USE_PTHREAD" class=LConfiguration id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')">USE_PTHREAD</a> or <a href="#USE_FORK" class=LConfiguration id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')">USE_FORK</a> to enable them.</p><p>It is valid to have a different implementation of <a href="core_portme-c.html#core_start_parallel" class=LFunction id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')">core_start_parallel</a> and &lt;core_end_parallel&gt; in <a href="core_portme-c.html#core_portme.c" class=LFile >core_portme.c</a>, to fit a particular architecture.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="USE_PTHREAD"></a>USE_PTHREAD</h3><div class=CBody><p>Sample implementation for launching parallel contexts This implementation uses pthread_thread_create and pthread_join.</p><h4 class=CHeading>Valid values</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>0</td><td class=CDLDescription>Do not use pthreads API.</td></tr><tr><td class=CDLEntry>1</td><td class=CDLDescription>Use pthreads API</td></tr></table><h4 class=CHeading>Note</h4><p>This flag only matters if MULTITHREAD has been defined to a value greater then 1.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="USE_FORK"></a>USE_FORK</h3><div class=CBody><p>Sample implementation for launching parallel contexts This implementation uses fork, waitpid, shmget,shmat and shmdt.</p><h4 class=CHeading>Valid values</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>0</td><td class=CDLDescription>Do not use fork API.</td></tr><tr><td class=CDLEntry>1</td><td class=CDLDescription>Use fork API</td></tr></table><h4 class=CHeading>Note</h4><p>This flag only matters if MULTITHREAD has been defined to a value greater then 1.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="USE_SOCKET"></a>USE_SOCKET</h3><div class=CBody><p>Sample implementation for launching parallel contexts This implementation uses fork, socket, sendto and recvfrom</p><h4 class=CHeading>Valid values</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>0</td><td class=CDLDescription>Do not use fork and sockets API.</td></tr><tr><td class=CDLEntry>1</td><td class=CDLDescription>Use fork and sockets API</td></tr></table><h4 class=CHeading>Note</h4><p>This flag only matters if MULTITHREAD has been defined to a value greater then 1.</p></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="MAIN_HAS_NOARGC"></a>MAIN_HAS_NOARGC</h3><div class=CBody><p>Needed if platform does not support getting arguments to main.</p><h4 class=CHeading>Valid values</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>0</td><td class=CDLDescription>argc/argv to main is supported</td></tr><tr><td class=CDLEntry>1</td><td class=CDLDescription>argc/argv to main is not supported</td></tr></table></div></div></div>

<div class="CConfiguration"><div class=CTopic><h3 class=CTitle><a name="MAIN_HAS_NORETURN"></a>MAIN_HAS_NORETURN</h3><div class=CBody><p>Needed if platform does not support returning a value from main.</p><h4 class=CHeading>Valid values</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>0</td><td class=CDLDescription>main returns an int, and return value will be 0.</td></tr><tr><td class=CDLEntry>1</td><td class=CDLDescription>platform does not support returning a value from main</td></tr></table></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="Variables"></a>Variables</h3></div></div>

<div class="CVariable"><div class=CTopic><h3 class=CTitle><a name="default_num_contexts"></a>default_num_contexts</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td>extern ee_u32 default_num_contexts</td></tr></table></blockquote><p>Number of contexts to spawn in multicore context.&nbsp; Override this global value to change number of contexts used.</p><h4 class=CHeading>Note</h4><p>This value may not be set higher then the <a href="#MULTITHREAD" class=LConfiguration id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')">MULTITHREAD</a> define.</p><p>To experiment, you can set the <a href="#MULTITHREAD" class=LConfiguration id=link6 onMouseOver="ShowTip(event, 'tt5', 'link6')" onMouseOut="HideTip('tt5')">MULTITHREAD</a> define to the highest value expected, and use argc/argv in the <a href="core_portme-c.html#portable_init" class=LFunction id=link7 onMouseOver="ShowTip(event, 'tt6', 'link7')" onMouseOut="HideTip('tt6')">portable_init</a> to set this value from the command line.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Copyright 2009 EEMBC</a></div><!--Footer-->


<div id=Menu><div class=MTitle>CoreMark<div class=MSubTitle>(c) EEMBC</div></div><div class=MEntry><div class=MFile><a href="../readme-txt.html">CoreMark</a></div></div><div class=MEntry><div class=MFile><a href="../release_notes-txt.html">Release Notes</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Source</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../core_list_join-c.html">core_list_join.c</a></div></div><div class=MEntry><div class=MFile><a href="../core_main-c.html">core_main.c</a></div></div><div class=MEntry><div class=MFile><a href="../core_matrix-c.html">core_matrix.c</a></div></div><div class=MEntry><div class=MFile><a href="../PIC32/core_portme-mak.html">core_portme.mak</a></div></div><div class=MEntry><div class=MFile><a href="../core_state-c.html">core_state.c</a></div></div><div class=MEntry><div class=MFile><a href="../core_util-c.html">core_util.c</a></div></div><div class=MEntry><div class=MFile><a href="../coremark-h.html">coremark.h</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">Porting</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="core_portme-c.html">PORT_DIR/<span class=HB> </span>core_portme.c</a></div></div><div class=MEntry><div class=MFile id=MSelected>PORT_DIR/<span class=HB> </span>core_portme.h</div></div><div class=MEntry><div class=MFile><a href="core_portme-mak.html">PORT_DIR/<span class=HB> </span>core_portme.mak</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Index</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MIndex><a href="../../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Types.html">Types</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Configuration.html">Configurations</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Variables.html">Variables</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/BuildTargets.html">Build Targets</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="BuildTargets">Build Targets</option><option value="Configuration">Configuration</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Types">Types</option><option value="Variables">Variables</option></select></div></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CVariable><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td>extern ee_u32 default_num_contexts</td></tr></table></blockquote>Number of contexts to spawn in multicore context. </div></div><div class=CToolTip id="tt2"><div class=CConfiguration>Sample implementation for launching parallel contexts This implementation uses pthread_thread_create and pthread_join.</div></div><div class=CToolTip id="tt3"><div class=CConfiguration>Sample implementation for launching parallel contexts This implementation uses fork, waitpid, shmget,shmat and shmdt.</div></div><div class=CToolTip id="tt4"><div class=CFunction>Start benchmarking in a parallel context.</div></div><div class=CToolTip id="tt5"><div class=CConfiguration>Define for parallel execution</div></div><div class=CToolTip id="tt6"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class=Prototype><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class=PBeforeParameters nowrap>void portable_init(</td><td class=PType nowrap>core_portable&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>p,</td></tr><tr><td></td><td class=PType nowrap>int&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argc,</td></tr><tr><td></td><td class=PType nowrap>char&nbsp;</td><td class=PParameterPrefix nowrap>*</td><td class=PParameter nowrap>argv[]</td><td class=PAfterParameters nowrap>)</td></tr></table></td></tr></table></blockquote>Target specific initialization code Test for some common mistakes.</div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>