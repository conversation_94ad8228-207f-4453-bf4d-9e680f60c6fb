<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script></head><body class="PopupSearchResultsPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Copyright 2009 EEMBC  -->
<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=SRStatus id=Loading>Loading...</div><table border=0 cellspacing=0 cellpadding=0><div class=SRResult id=SR_default_undnum_undcontexts><div class=IEntry><a href="../files/linux/core_portme-h.html#default_num_contexts" target=_parent class=ISymbol>default_num_contexts</a></div></div><div class=SRResult id=SR_Description><div class=IEntry><a href="javascript:searchResults.Toggle('SR_Description')" class=ISymbol>Description</a><div class=ISubIndex><a href="../files/core_list_join-c.html#Description" target=_parent class=IFile>core_list_join.c</a><a href="../files/core_matrix-c.html#Description" target=_parent class=IFile>core_matrix.c</a><a href="../files/core_state-c.html#Description" target=_parent class=IFile>core_state.c</a><a href="../files/coremark-h.html#Description" target=_parent class=IFile>coremark.h</a><a href="../files/linux/core_portme-h.html#Description" target=_parent class=IFile>linux/<span class=HB> </span>core_portme.h</a></div></div></div><div class=SRResult id=SR_Documentation><div class=IEntry><a href="../files/readme-txt.html#Documentation" target=_parent class=ISymbol>Documentation</a></div></div></table><div class=SRStatus id=Searching>Searching...</div><div class=SRStatus id=NoMatches>No Matches</div><script type="text/javascript"><!--
document.getElementById("Loading").style.display="none";
document.getElementById("NoMatches").style.display="none";
var searchResults = new SearchResults("searchResults", "HTML");
searchResults.Search();
--></script></div><script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>