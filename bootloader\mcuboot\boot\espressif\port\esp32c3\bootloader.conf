# SPDX-FileCopyrightText: 2021 Espressif Systems (Shanghai) CO LTD
#
# SPDX-License-Identifier: Apache-2.0

CONFIG_ESP_FLASH_SIZE=4MB
CONFIG_ESP_BOOTLOADER_SIZE=0xF000
CONFIG_ESP_BOOTLOADER_OFFSET=0x0000
CONFIG_ESP_IMAGE0_PRIMARY_START_ADDRESS=0x10000
CONFIG_ESP_APPLICATION_SIZE=0x100000
CONFIG_ESP_IMAGE0_SECONDARY_START_ADDRESS=0x110000
CONFIG_ESP_MCUBOOT_WDT_ENABLE=y
CONFIG_ESP_SCRATCH_OFFSET=0x210000
CONFIG_ESP_SCRATCH_SIZE=0x40000

# When enabled, prevents updating image to an older version
# CONFIG_ESP_DOWNGRADE_PREVENTION=y
# This option makes downgrade prevention rely also on security
# counter (defined using imgtool) instead of only image version
# CONFIG_ESP_DOWNGRADE_PREVENTION_SECURITY_COUNTER=y

# Enables the MCUboot Serial Recovery, that allows the use of
# MCUMGR to upload a firmware through the serial port
# CONFIG_ESP_MCUBOOT_SERIAL=y
# Use Serial through USB JTAG Serial port for Serial Recovery
# CONFIG_ESP_MCUBOOT_SERIAL_USB_SERIAL_JTAG=y
# Use sector erasing (recommended) instead of entire image size
# erasing when uploading through Serial Recovery
# CONFIG_ESP_MCUBOOT_ERASE_PROGRESSIVELY=y

# GPIO used to boot on Serial Recovery
# CONFIG_ESP_SERIAL_BOOT_GPIO_DETECT=5
# GPIO input type (0 for Pull-down, 1 for Pull-up)
# CONFIG_ESP_SERIAL_BOOT_GPIO_INPUT_TYPE=0
# GPIO signal value
# CONFIG_ESP_SERIAL_BOOT_GPIO_DETECT_VAL=1
# Delay time for identify the GPIO signal
# CONFIG_ESP_SERIAL_BOOT_DETECT_DELAY_S=5
# UART port used for serial communication (not needed when using USB)
# CONFIG_ESP_SERIAL_BOOT_UART_NUM=1
# GPIO for Serial RX signal
# CONFIG_ESP_SERIAL_BOOT_GPIO_RX=8
# GPIO for Serial TX signal
# CONFIG_ESP_SERIAL_BOOT_GPIO_TX=9

# Use UART0 for console printing (use either UART or USB alone)
CONFIG_ESP_CONSOLE_UART=y
CONFIG_ESP_CONSOLE_UART_NUM=0
# Configures alternative UART port for console printing
# (UART_NUM=0 must not be changed)
# CONFIG_ESP_CONSOLE_UART_CUSTOM=y
# CONFIG_ESP_CONSOLE_UART_TX_GPIO=9
# CONFIG_ESP_CONSOLE_UART_RX_GPIO=8
# Use USB JTAG Serial for console printing
# CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG=y

# CONFIG_ESP_SIGN_EC256=y
# CONFIG_ESP_SIGN_ED25519=n
# CONFIG_ESP_SIGN_RSA=n
# CONFIG_ESP_SIGN_RSA_LEN=2048

# Use Tinycrypt lib for EC256 or ED25519 signing
# CONFIG_ESP_USE_TINYCRYPT=y
# Use Mbed TLS lib for RSA image signing
# CONFIG_ESP_USE_MBEDTLS=n

# It is strongly recommended to generate a new signing key
# using imgtool instead of use the existent sample
# CONFIG_ESP_SIGN_KEY_FILE=root-ec-p256.pem

# Hardware Secure Boot related options
# CONFIG_SECURE_SIGNED_ON_BOOT=1
# CONFIG_SECURE_SIGNED_APPS_RSA_SCHEME=1
# CONFIG_SECURE_BOOT=1
# CONFIG_SECURE_BOOT_V2_ENABLED=1
# CONFIG_SECURE_BOOT_SUPPORTS_RSA=1

# Hardware Flash Encryption related options
# CONFIG_SECURE_FLASH_ENC_ENABLED=1
# CONFIG_SECURE_FLASH_UART_BOOTLOADER_ALLOW_ENC=1
# CONFIG_SECURE_FLASH_UART_BOOTLOADER_ALLOW_DEC=1
# CONFIG_SECURE_FLASH_UART_BOOTLOADER_ALLOW_CACHE=1
# CONFIG_SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT=1
# CONFIG_SECURE_BOOT_ALLOW_JTAG=1
# CONFIG_SECURE_BOOT_ALLOW_ROM_BASIC=1

# Options for enabling eFuse emulation in Flash
# CONFIG_EFUSE_VIRTUAL=1
# CONFIG_EFUSE_VIRTUAL_KEEP_IN_FLASH=1
